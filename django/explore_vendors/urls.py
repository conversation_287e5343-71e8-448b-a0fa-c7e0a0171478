from django.urls import path
from .views import (
    ListVendorsAPI,
    VendorDetailsAPI,
    RegisterVendorAPI,
    OnboardVendorAPI,
)


urlpatterns = [
    path('vendors/listing/', ListVendorsAPI.as_view(), name='list_vendors'),
    path('vendor/<str:company_id>/', VendorDetailsAPI.as_view(), name='vendor_details'),
    path('vendor/<str:company_id>/register/', RegisterVendorAPI.as_view(), name='register_vendor'),
    path('vendor/<str:company_id>/onboard/', OnboardVendorAPI.as_view(), name='onboard_vendor'),
]
