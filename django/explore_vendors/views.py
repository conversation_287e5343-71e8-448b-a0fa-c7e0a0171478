import logging
from rest_framework import status
from django.conf import settings
from django.utils.decorators import method_decorator
from utils.mongo import MongoUtility
from utils.constants import (
    AdminDBColls,
    CompanyType,
    AttachmentType,
    SAASModules
)
from utils import (
    format_response,
    get_presigned_url,
    get_search_results_from_google_geocode_api
)
from authn.decorators import validate_data_access, validate_query_params
from authn import (
    AuthenticateAll,
    AuthenticateSeeker,
    PermissionedAPIView,
    Permissions,
)
from .request_validators import ListVendorsPayloadValidator

logger = logging.getLogger('application')


@method_decorator(validate_query_params(ListVendorsPayloadValidator), name='get')
class ListVendorsAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateSeeker, )
    permissions = [
        (Permissions.ExploreVendors.VIEW, ['GET']),
    ]

    def get(self, request, *args, **kwargs):
        params = request.validated_params
        self.presigned_link_expiry = 21600  # valid for 6hours

        db = MongoUtility(module_id=SAASModules.ADMIN.value)

        df = {
            '_id': 0,
            'id': 1,
            'name': 1,
            'company_type': 1,
            'logo': 1,
            'rating': 1,
            'total_ratings': 1,
            'attachments': 1,
            'vehicle_details': 1,
        }
        query = {
            'company_type': CompanyType.PROVIDER.value,
            # 'is_sclen_vendor': True
        }

        if params.search_term:
            query['$or'] = [
                {'name': {'$regex': params.search_term, '$options': 'i'}},
                {'email': {'$regex': params.search_term, '$options': 'i'}},
            ]
        elif params.similar_to:
            vendor = db.find(AdminDBColls.COMPANIES, {'id': params.similar_to}, find_one=True)
            if vendor.get('operational_areas'):
                query['operational_areas.id'] = {'$in': [x['id'] for x in vendor['operational_areas']]}
        elif params.from_place_id:
            from_state_code = get_search_results_from_google_geocode_api(place_id=params.from_place_id)['results'][0]['state_code']
            to_state_code = get_search_results_from_google_geocode_api(place_id=params.to_place_id)['results'][0]['state_code']
            query['operational_areas.id'] = {'$in': [from_state_code, to_state_code]}

        vendor_docs = db.find(AdminDBColls.COMPANIES, query, df, sort=[(params.sort_by, params.sort_order)]).limit(params.limit).skip(params.offset)
        total_vendors = vendor_docs.count()

        vendors = []
        for vendor in vendor_docs:
            if params.preview_logo:
                vendor['logo'] = self.get_presigned_url_for_logo(vendor.get('logo'))
            vendors.append(vendor)

        response_data = {
            'vendors': vendors,
            'total_count': total_vendors
        }
        return format_response(status.HTTP_200_OK, response_data, 'Success')

    def get_presigned_url_for_logo(self, logo_url):
        if not logo_url:
            return logo_url

        url, error = get_presigned_url(
            file_url=logo_url,
            s3_bucket=settings.SAAS_S3_BUCKET,
            link_expiry=self.presigned_link_expiry
        )
        return url if not error else logo_url


class VendorDetailsAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateAll, )
    permissions = [
        (Permissions.ExploreVendors.VIEW, ['GET']),
    ]

    @validate_data_access(['vendor_company'])
    def get(self, request, *args, **kwargs):
        company_doc = request.company_doc

        params = request.GET
        preview_logo = int(params.get('preview_logo', 0))
        preview_docs = int(params.get('preview_docs', 0))

        self.presigned_link_expiry = 21600  # valid for 6hours

        if preview_logo:
            company_doc['logo'] = self.get_presigned_url_for_logo(company_doc.get('logo'))

        if preview_docs:
            company_doc['attachments'] = self.get_presigned_url_for_docs(company_doc.get('attachments') or [])

        if request.is_seeker:
            db = MongoUtility(module_id=SAASModules.ADMIN.value)

            query = {'seeker_id': request.company_id, 'provider_id': company_doc['id']}
            mapping_doc = db.find(AdminDBColls.PROVIDER_MAPPING, query, {'_id': 1}, find_one=True)

            company_doc['is_linked'] = bool(mapping_doc)

        response_data = company_doc
        return format_response(status.HTTP_200_OK, response_data, 'Success')

    def get_presigned_url_for_logo(self, logo_url):
        if not logo_url:
            return logo_url

        url, error = get_presigned_url(
            file_url=logo_url,
            s3_bucket=settings.SAAS_S3_BUCKET,
            link_expiry=self.presigned_link_expiry
        )
        return url if not error else logo_url

    def get_presigned_url_for_docs(self, documents):
        for doc in documents:
            if doc['type'] not in [AttachmentType.OFFICE.value, AttachmentType.FLEET.value]:
                continue

            url, error = get_presigned_url(
                file_url=doc['url'],
                s3_bucket=settings.SAAS_S3_BUCKET,
                link_expiry=self.presigned_link_expiry
            )
            if not error:
                doc['url'] = url
        return documents


class RegisterVendorAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateSeeker, )
    permissions = [
        (Permissions.ExploreVendors.REGISTER, ['POST']),
    ]

    @validate_data_access(['vendor_company'])
    def post(self, request, *args, **kwargs):
        vendor = request.company_doc
        seeker_id = request.company_id
        provider_id = vendor['id']

        db = MongoUtility(module_id=SAASModules.ADMIN.value)

        mapping_doc = {
            'seeker_id': seeker_id,
            'seeker_name': request.company_name,
            'provider_id': provider_id,
            'provider_name': vendor['name']
        }
        query = {
            'seeker_id': seeker_id,
            'provider_id': provider_id
        }
        db.update(AdminDBColls.COMPANY_MAPPING, query, mapping_doc, upsert=True)
        return format_response(status.HTTP_200_OK, {}, 'Success')


class OnboardVendorAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateSeeker, )
    permissions = [
        (Permissions.ExploreVendors.ONBOARD, ['POST']),
    ]

    def post(self, request, *args, **kwargs):
        return format_response(status.HTTP_200_OK, {}, 'Success')
