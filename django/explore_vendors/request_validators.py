import logging
from pydantic import (
    model_validator,
    BaseModel,
    Field,
)
from typing_extensions import Self

logger = logging.getLogger('application')


class ListVendorsPayloadValidator(BaseModel):
    limit: int = Field(default=10, gt=0, le=20)
    offset: int = Field(default=0, ge=0)
    sort_by: str = Field(default='name')
    sort_order: int = Field(default=-1, in_=[1, -1])
    preview_logo: int | None = Field(default=0, in_=[0, 1])
    search_term: str | None = Field(default='', min_length=3, max_length=64)
    from_place_id: str | None = Field(default=None)
    to_place_id: str | None = Field(default=None)
    similar_to: str | None = None

    @model_validator(mode='after')
    def validate_from_to_fields(self) -> Self:
        if (self.from_place_id and not self.to_place_id) or (self.to_place_id and not self.from_place_id):
            raise ValueError('Please enter both From and To locations')
        return self
