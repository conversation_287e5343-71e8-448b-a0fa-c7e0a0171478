import logging
from rest_framework import status
from pydantic import ValidationError
from django.utils.decorators import method_decorator
from authn.decorators import validate_data_access, validate_request_payload
from authn import (
    Authenti<PERSON><PERSON>eeker,
    AuthenticateAllOrAccessToken,
    Permissions,
    PermissionedAPIView
)
from utils import (
    format_error_response,
    format_response,
    ConfigError
)
from utils.mongo import MongoUtility
from utils.constants import (
    AdminDBColls, NetworkDBColls,
    OBFormSections, OBFormStatus,
    CompanyType, EmailType,
    SAASModules
)
from schema import RemarksModel
from .request_validators import OBFormRemarksPayloadValidator
from .onboarding_utils import send_onboarding_form_email

logger = logging.getLogger('application')


class VerifySectionAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateSeeker, )
    permissions = [
        (Permissions.VendorOnboarding.REVIEW, ['POST']),
    ]

    @validate_data_access(['onboarding_form'])
    def post(self, request, *args, **kwargs):
        form_doc = request.form_doc

        if form_doc['form_status_id'] != OBFormStatus.REVIEW.value:
            return format_error_response(status.HTTP_400_BAD_REQUEST, "Form is not yet submitted for review.")

        section_id = request.data.get('section_id')
        is_verified = bool(request.data.get('is_verified', False))

        if section_id not in OBFormSections:
            return format_error_response(status.HTTP_400_BAD_REQUEST, "Invalid section_id.")

        db = MongoUtility()

        update_query = {
            'attachments.$[elem].verified_on': request.now if is_verified else None,
            'updated_on': request.now
        }
        array_filters = [{'elem.type': section_id}]

        db.update(NetworkDBColls.ONBOARDING_FORMS, {'id': form_doc['id']}, update_query, array_filters=array_filters)

        return format_response(status.HTTP_200_OK, {}, 'Success')


class ListRemarksAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateAllOrAccessToken, )
    permissions = [
        (Permissions.VendorOnboarding.VIEW, ['GET']),
    ]

    @validate_data_access(['onboarding_form'])
    def get(self, request, *args, **kwargs):
        form_doc = request.form_doc
        db = MongoUtility()

        df = {
            '_id': 0,
            'id': 1,
            'section_id': 1,
            'remark': 1,
            'is_active': 1,
            'is_resolved': 1,
        }
        query = {
            'form_id': form_doc['id'],
            'provider_id': form_doc['company_id'],
            # 'is_resolved': False
        }
        if request.is_seeker:
            query['seeker_id'] = request.company_id

        remarks = db.find(NetworkDBColls.ONBOARDING_FORM_REMARKS, query, df)
        response_data = {
            'remarks': [x for x in remarks]
        }
        return format_response(status.HTTP_200_OK, response_data, "Success")


@method_decorator(validate_request_payload(OBFormRemarksPayloadValidator), name='post')
class RemarkCRUDAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateSeeker, )
    permissions = [
        (Permissions.VendorOnboarding.REVIEW, ['POST', 'PUT', 'DELETE']),
    ]

    @validate_data_access(['onboarding_form'])
    def post(self, request, *args, **kwargs):
        payload = request.validated_payload.model_dump()

        db = MongoUtility()

        remarks = []
        for remark in payload['remarks']:
            remark.update({
                'form_id': kwargs['form_id'],
                'provider_id': kwargs['company_id'],
                'seeker_id': request.company_id,
                'created_by': request.user_name,
                'created_by_id': request.user_id,
            })

            try:
                doc = RemarksModel(**remark).model_dump()
            except ValidationError as e:
                return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)

            remarks.append(doc)

        if remarks:
            db.insert(NetworkDBColls.ONBOARDING_FORM_REMARKS, remarks, insert_many=True)

        return format_response(status.HTTP_200_OK, {}, "Success")

    @validate_data_access(['ob_form_remark'])
    def put(self, request, *args, **kwargs):
        remark_doc = request.remark_doc

        if not remark_doc['is_active']:
            return format_error_response(status.HTTP_400_BAD_REQUEST, f"Remark hasn't been submitted yet.")

        is_resolved = bool(request.data.get('is_resolved', False))

        db = MongoUtility()

        update_query = {'is_resolved': is_resolved, 'updated_on': request.now}

        db.update(NetworkDBColls.ONBOARDING_FORM_REMARKS, {'id': remark_doc['id']}, update_query)

        return format_response(status.HTTP_200_OK, {}, "Success")

    @validate_data_access(['ob_form_remark'])
    def delete(self, request, *args, **kwargs):
        remark_doc = request.remark_doc

        if remark_doc['is_active']:
            return format_error_response(status.HTTP_400_BAD_REQUEST, f"Submitted remarks cannot be deleted.")

        db = MongoUtility()

        db.delete(NetworkDBColls.ONBOARDING_FORM_REMARKS, {'id': remark_doc['id']})

        return format_response(status.HTTP_200_OK, {}, "Success")


class ReSendOnboardingFormAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateSeeker, )
    permissions = [
        (Permissions.VendorOnboarding.SEND_FORM, ['POST']),
    ]

    @validate_data_access(['onboarding_form'])
    def post(self, request, *args, **kwargs):
        form_doc = request.form_doc
        form_id = form_doc['id']

        db = MongoUtility()

        if form_doc['form_status_id'] != OBFormStatus.REVIEW.value:
            return format_error_response(status.HTTP_400_BAD_REQUEST, f"Form is not yet submitted for review.")

        try:
            send_onboarding_form_email(
                form_id=form_id,
                vendor_id=form_doc['company_id'],
                vendor_name=form_doc['name'],
                vendor_email=form_doc['email'],
                email_type=EmailType.VENDOR_OB_FORM_CORRECTIONS,
            )
        except ConfigError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, str(e))
        except KeyError:
            return format_error_response(status.HTTP_400_BAD_REQUEST, "Improperly configured onboarding email config.")

        update_query = {
            'form_status': OBFormStatus.PENDING.name,
            'form_status_id': OBFormStatus.PENDING.value,
            'updated_on': request.now
        }
        db.update(NetworkDBColls.ONBOARDING_FORMS, {'id': form_id}, update_query)

        query = {
            'form_id': form_id,
            'seeker_id': request.company_id,
            'is_active': False
        }
        update_query = {'is_active': True, 'updated_on': request.now}
        db.update(NetworkDBColls.ONBOARDING_FORM_REMARKS, query, update_query, update_many=True)

        return format_response(status.HTTP_200_OK, {}, "Success")


class ApproveOnboardingFormAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateSeeker, )
    permissions = [
        (Permissions.VendorOnboarding.REVIEW, ['POST']),
    ]

    @validate_data_access(['onboarding_form'])
    def post(self, request, *args, **kwargs):
        form_doc = request.form_doc
        form_id = form_doc['id']

        db = MongoUtility()
        admin_db = MongoUtility(module_id=SAASModules.ADMIN.value)

        if form_doc['form_status_id'] == OBFormStatus.APPROVED.value:
            return format_error_response(status.HTTP_400_BAD_REQUEST, f"Form is already approved.")
        elif form_doc['form_status_id'] != OBFormStatus.REVIEW.value:
            return format_error_response(status.HTTP_400_BAD_REQUEST, f"Form is not yet submitted for review.")

        for doc in form_doc['attachments']:
            if not doc.get('verified_on'):
                return format_error_response(status.HTTP_400_BAD_REQUEST, f"Please verify all the attached documents.")

        query = {
            'form_id': form_id,
            'seeker_id': request.company_id,
            'is_resolved': False
        }
        unresolved_remark = db.find(NetworkDBColls.ONBOARDING_FORM_REMARKS, query, find_one=True)
        if unresolved_remark:
            section = unresolved_remark['section_id'].replace('_', ' ').title()
            return format_error_response(status.HTTP_400_BAD_REQUEST, f'Please resolve the unresolved remarks in "{section}" section.')

        update_query = {
            'form_status': OBFormStatus.APPROVED.name,
            'form_status_id': OBFormStatus.APPROVED.value,
            'updated_on': request.now
        }
        db.update(NetworkDBColls.ONBOARDING_FORMS, {'id': form_id}, update_query)

        vendor_id = form_doc['company_id']
        vendor_company = form_doc.copy()

        unwanted_keys = [
            'id', 'form_status_id', 'form_status', 'company_id',
            'seeker_id', 'is_tnc_accepted', 'tnc_accepted_on',
            'last_seen_on', 'created_on', 'updated_on',
        ]
        for key in unwanted_keys:
            try:
                del vendor_company[key]
            except KeyError:
                pass

        vendor_company.update({
            'updated_on': request.now
        })

        admin_db.update(AdminDBColls.COMPANIES, {'id': vendor_id, 'company_type': CompanyType.PROVIDER.value}, vendor_company, upsert=True)

        return format_response(status.HTTP_200_OK, {}, "Success")
