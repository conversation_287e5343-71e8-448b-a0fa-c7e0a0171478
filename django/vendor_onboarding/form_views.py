import json
import logging
from pydantic import ValidationError
from rest_framework import status
from rest_framework.parsers import MultiPartParser, FormParser
from django.utils.decorators import method_decorator
from django.conf import settings
from authn.decorators import validate_data_access, validate_query_params
from authn import (
    AuthenticateAll,
    AuthenticateAllOrAccessToken,
    AuthenticateProviderOrAccessToken,
    Permissions,
    PermissionedAPIView
)
from utils import (
    format_response, format_error_response,
    Memoization, validate_file_type_and_size,
    clean_filename, get_presigned_url,
    upload_to_s3, delete_from_s3,
    get_address_components, DateUtil,
    restructure_services_offered
)
from utils.mongo import MongoUtility
from utils.constants import (
    AdminDBColls, NetworkDBColls,
    SAASModules, CompanyType,
    FileTypes, OBFormStatus,
    VideoTypes
)
from .request_validators import (
    ListOBFormsParamsValidator,
    GetFormDataParamsValidator,
    OnboardCompanyDataPayloadValidator,
    OnboardServiceDataPayloadValidator
)
from schema import AttachmentSchema, OnboardingFormSchema

logger = logging.getLogger('application')


@method_decorator(validate_query_params(ListOBFormsParamsValidator), name='get')
class ListOnboardingFormsAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateAll, )
    permissions = [
        (Permissions.VendorOnboarding.VIEW, ['GET']),
    ]

    def get(self, request, *args, **kwargs):
        params = request.validated_params

        db = MongoUtility()

        query = {
            'form_status_id': params.form_status_id,
            'company_type': CompanyType.PROVIDER.value
        }
        if request.is_provider:
            query['company_id'] = request.company_id
        else:
            query['seeker_id'] = request.company_id

        if params.search_term:
            query['name'] = {'$regex': params.search_term, '$options': 'i'}

        df = {
            '_id': 0,
            'id': 1,
            'company_id': 1,
            'name': 1,
            'email': 1,
            'form_status': 1,
            'form_status_id': 1,
            'created_on': 1,
            'updated_on': 1,
            'last_seen_on': 1,
        }
        ob_forms = db.find(NetworkDBColls.ONBOARDING_FORMS, query, df, sort=[(params.sort_by, params.sort_order)]).limit(params.limit).skip(params.offset)
        total = ob_forms.count()

        response_data = {
            'onboarding_forms': [x for x in ob_forms],
            'total': total
        }
        return format_response(status.HTTP_200_OK, response_data, "Success")


class CountOnboardingFormsAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateAll, )
    permissions = [
        (Permissions.VendorOnboarding.VIEW, ['GET']),
    ]

    def get(self, request, *args, **kwargs):
        search_term = request.GET.get('search_term')

        self.db = MongoUtility()

        query = {
            'company_type': CompanyType.PROVIDER.value
        }
        if request.is_provider:
            query['company_id'] = request.company_id
        else:
            query['seeker_id'] = request.company_id

        if search_term:
            query['name'] = {'$regex': search_term, '$options': 'i'}

        response_data = {
            'totals': self.get_total_counts(query)
        }
        return format_response(status.HTTP_200_OK, response_data, "Success")

    def get_total_counts(self, query):
        query['form_status_id'] = {'$in': [x.value for x in OBFormStatus]}

        group_query = {
            "_id": "$form_status_id",
            "count": {"$sum": 1}
        }

        project_query = {
            "_id": 0,
            "form_status_id": "$_id",
            "count": 1
        }

        aggregate_result = list(self.db.aggregate(NetworkDBColls.ONBOARDING_FORMS, query, group_query, project_query))
        return aggregate_result


@method_decorator(validate_query_params(GetFormDataParamsValidator), name='get')
class GetOnboardingFormDataAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateAllOrAccessToken, )
    permissions = [
        (Permissions.VendorOnboarding.VIEW, ['GET']),
    ]

    def get(self, request, *args, **kwargs):
        params = request.validated_params
        form_id = params.form_id
        preview_logo = params.preview_logo
        preview_docs = params.preview_docs
        self.presigned_link_expiry = 21600  # valid for 6hours

        db = MongoUtility()

        query = {
            'company_type': CompanyType.PROVIDER.value
        }

        if request.is_provider:
            query['company_id'] = request.company_id
            if not form_id:
                query['form_status_id'] = {'$in': [OBFormStatus.PENDING.value, OBFormStatus.DRAFT.value]}
            else:
                query['id'] = form_id
        else:
            query.update({
                'id': form_id,
                'company_id': kwargs['company_id'],
            })

        form_doc = db.find(NetworkDBColls.ONBOARDING_FORMS, query, find_one=True)
        if not form_doc:
            return format_error_response(status.HTTP_404_NOT_FOUND, 'Form not found.')

        if request.company_type == CompanyType.PROVIDER.value:
            # tracking last seen status
            query['id'] = form_doc['id']
            db.update(NetworkDBColls.ONBOARDING_FORMS, query, {'last_seen_on': DateUtil.get_current_timestamp()}, upsert=True)

        services_offered = form_doc.get('services_offered') or []
        form_doc['services_offered'] = restructure_services_offered(services_offered)

        if preview_logo:
            form_doc['logo'] = self.get_presigned_url_for_logo(form_doc['logo'])

        if preview_docs:
            form_doc['attachments'] = self.get_presigned_url_for_docs(form_doc['attachments'])

        response_data = form_doc
        return format_response(status.HTTP_200_OK, response_data, "Success")

    def get_presigned_url_for_logo(self, logo_url):
        if not logo_url:
            return logo_url

        url, error = get_presigned_url(
            file_url=logo_url,
            s3_bucket=settings.SAAS_S3_BUCKET,
            link_expiry=self.presigned_link_expiry
        )
        return url if not error else logo_url

    def get_presigned_url_for_docs(self, documents):
        for doc in documents:
            url, error = get_presigned_url(
                file_url=doc['url'],
                s3_bucket=settings.SAAS_S3_BUCKET,
                link_expiry=self.presigned_link_expiry
            )
            if not error:
                doc['url'] = url
        return documents


class OnboardCompanyDataAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateProviderOrAccessToken, )
    permissions = [
        (Permissions.VendorOnboarding.UPDATE, ['POST', 'DELETE']),
    ]
    parser_classes = (MultiPartParser, FormParser)

    @validate_data_access(['onboarding_form'])
    def post(self, request, *args, **kwargs):
        form_doc = request.form_doc
        form_id = form_doc.get('id')

        if form_doc.get('form_status_id') in [OBFormStatus.REVIEW.value, OBFormStatus.APPROVED.value]:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Form has already been submitted.')

        data = json.loads(request.data.get('company_data', '{}'))
        strict_validation = data.get('strict_validation', False)

        if strict_validation and request.FILES.get('logo'):
            # in case user adds all mandatory fields in the first attempt and submits
            # in order to avoid validation error on logo and orphan files in s3
            # adding a dummy url
            data['logo'] = 'https://www.example.com'

        DynamicModel = OnboardCompanyDataPayloadValidator.configure_mandatory(strict=strict_validation)

        try:
            validated_data = DynamicModel(**data).model_dump()
        except ValidationError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)

        db = MongoUtility()
        admin_db = MongoUtility(module_id=SAASModules.ADMIN.value)

        if validated_data['admin_details']:
            query = {
                'company_id': {'$ne': request.company_id},
                'company_type': CompanyType.PROVIDER.value,
                'email': validated_data['admin_details'].get('email'),
            }
            user_doc = admin_db.find(AdminDBColls.USERS, query, find_one=True)
            if user_doc and user_doc['company_id'] != request.company_id:
                return format_error_response(status.HTTP_400_BAD_REQUEST, f"User with email({user_doc['email']}) already exists.")

        logo = form_doc.get('logo')
        if (not logo) and ('logo' in request.FILES):
            logo, s3_error = self.upload_company_logo(request)
            if s3_error:
                return format_error_response(status.HTTP_400_BAD_REQUEST, s3_error)

        query = {
            'id': form_id,
            'company_id': request.company_id,
            'company_type': CompanyType.PROVIDER.value
        }
        updated_company_data = {
            **validated_data,
            'company_type': CompanyType.PROVIDER.value,
            'updated_on': request.now,
            'logo': logo
        }
        self.update_location_details(new_form_data=updated_company_data, old_form_data=form_doc)

        db.update(NetworkDBColls.ONBOARDING_FORMS, query, updated_company_data)

        return format_response(status.HTTP_200_OK, {}, "Success")

    def upload_company_logo(self, request):
        file = request.FILES['logo']
        allowed_types = [FileTypes.JPG.value, FileTypes.JPEG.value, FileTypes.PNG.value]

        is_file_valid, errors = validate_file_type_and_size(file, allowed_types)
        if not is_file_valid:
            return format_error_response(status.HTTP_400_BAD_REQUEST, errors[0])

        original_file_name = file.name
        clean_file_name = clean_filename(original_file_name, add_timestamp=True)
        file_path = '{}/{}/{}/{}'.format(
            settings.ENVIRONMENT_ENV,
            SAASModules.VENDOR_ONBOARDING.name.lower(),
            request.company_id,
            clean_file_name
        )

        s3_path, s3_error = upload_to_s3(file, file_path)
        return s3_path, s3_error

    def update_location_details(self, new_form_data, old_form_data=None):
        if not old_form_data:
            old_form_data = {}

        old_place_id = old_form_data.get('place_id')
        new_place_id = new_form_data.get('place_id')

        if new_place_id and (old_place_id != new_place_id):
            data = get_address_components(new_place_id, new_form_data['lat'], new_form_data['lng'])
            new_form_data.update(data)

        address_sections = ['regional_offices', 'branch_offices', 'customers']
        address_components = ['address', 'place_id', 'lat', 'lng', 'place_name', 'city', 'state', 'state_code', 'pincode']
        old_items_map = {}
        for section in address_sections:
            old_section_data = old_form_data.get(section) or []
            old_items_map.update({x['place_id']: x for x in old_section_data})

            new_items = new_form_data[section] or []
            for item in new_items:
                new_place_id = item['place_id']
                lat, lng = item['lat'], item['lng']

                if (new_place_id in old_items_map) and (old_items_map[new_place_id]['pincode']):
                    # make sure old data is not overwritten in case there aren't any new changes
                    for key in address_components:
                        item[key] = old_items_map[new_place_id][key]
                    continue

                if new_place_id and lat and lng:
                    data = get_address_components(new_place_id, lat, lng)
                    item.update(data)

    @validate_data_access(['onboarding_form'])
    def delete(self, request, *args, **kwargs):
        form_doc = request.form_doc
        logo_url = request.data.get('logo')

        if form_doc['form_status_id'] in [OBFormStatus.REVIEW.value, OBFormStatus.APPROVED.value]:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Form has already been submitted.')

        if (not logo_url) or (logo_url != form_doc.get('logo')):
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Invalid logo url recieved.')
        elif request.company_id not in logo_url:
            return format_error_response(status.HTTP_404_NOT_FOUND, 'Logo not found.')

        error = delete_from_s3(file_url=logo_url)
        if error:
            return format_error_response(status.HTTP_400_BAD_REQUEST, error)

        db = MongoUtility()

        query = {
            'id': form_doc['id'],
            'company_id': request.company_id,
            'company_type': CompanyType.PROVIDER.value
        }
        update_query = {'logo': None, 'updated_on': request.now}

        db.update(NetworkDBColls.ONBOARDING_FORMS, query, update_query)
        return format_response(status.HTTP_200_OK, {}, 'Success.')


class OnboardServiceDataAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateProviderOrAccessToken, )
    permissions = [
        (Permissions.VendorOnboarding.UPDATE, ['POST']),
    ]

    @validate_data_access(['onboarding_form'])
    def post(self, request, *args, **kwargs):
        form_doc = request.form_doc
        data = request.data
        strict_validation = data.get('strict_validation', False)

        if form_doc['form_status_id'] in [OBFormStatus.REVIEW.value, OBFormStatus.APPROVED.value]:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Form has already been submitted.')

        DynamicModel = OnboardServiceDataPayloadValidator.configure_mandatory(strict=strict_validation)

        try:
            validated_data = DynamicModel(**data).model_dump()
        except ValidationError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)

        db = MongoUtility()

        validated_data['updated_on'] = request.now

        query = {
            'id': form_doc['id'],
            'company_id': request.company_id,
            'company_type': CompanyType.PROVIDER.value
        }

        db.update(NetworkDBColls.ONBOARDING_FORMS, query, validated_data)
        return format_response(status.HTTP_200_OK, {}, "Success")


class OnboardDocumentsAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateAllOrAccessToken, )
    permissions = [
        (Permissions.VendorOnboarding.VIEW, ['GET']),
        (Permissions.VendorOnboarding.UPDATE, ['POST', 'DELETE']),
    ]

    @validate_data_access(['onboarding_form'])
    def get(self, request, *args, **kwargs):
        form_doc = request.form_doc

        attachment_id = kwargs['attachment_id']
        direct_download = request.GET.get('action', 'preview') == 'download'

        for attachment in form_doc.get('attachments', []):
            if attachment['id'] != attachment_id:
                continue

            url, error = get_presigned_url(
                file_url=attachment['url'],
                for_download=direct_download,
                s3_bucket=settings.SAAS_S3_BUCKET,
                link_expiry=settings.PRESIGNED_LINK_EXPIRY
            )
            if error:
                return format_error_response(status.HTTP_400_BAD_REQUEST, error)

            response_data = {'url': url}
            return format_response(status.HTTP_200_OK, response_data, 'Attachment link generated successfully')

        return format_error_response(status.HTTP_404_NOT_FOUND, "Attachment not found.")

    @validate_data_access(['onboarding_form'])
    def post(self, request, *args, **kwargs):
        form_doc = request.form_doc

        if form_doc['form_status_id'] in [OBFormStatus.REVIEW.value, OBFormStatus.APPROVED.value]:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Form has already been submitted.')

        docs_count, videos_count = {}, {}

        existing_attachments = form_doc.get('attachments') or []
        for att in existing_attachments:
            doc_type = att['type']
            if att['file_type'] in VideoTypes:
                videos_count[doc_type] = videos_count.get(doc_type, 0) + 1
            else:
                docs_count[doc_type] = docs_count.get(doc_type, 0) + 1

        attachment_type = request.data.get('attachment_type')
        doc_types = Memoization.get_dropdown_data(AdminDBColls.ONBOARDING_DOCUMENT_TYPES)

        try:
            doc_type_config = [x for x in doc_types if x['id'] == attachment_type][0]
        except IndexError:
            return format_error_response(status.HTTP_400_BAD_REQUEST, f'Invalid attachment_type({attachment_type}) recieved.')

        db = MongoUtility()

        files = request.data.getlist('files', [])
        if not files:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Please attach file to upload.')

        doc_name = doc_type_config['name']
        allowed_types = doc_type_config['valid_document_formats'] + doc_type_config.get('valid_video_formats', [])
        max_file_size = doc_type_config['max_document_size'] * 1024 * 1024
        max_video_size = doc_type_config.get('max_video_size', 0) * 1024 * 1024
        max_no_of_files = doc_type_config.get('max_files') or 1
        max_no_of_videos = doc_type_config.get('max_videos') or 0

        new_attachments = []
        for file in files:
            original_file_name = file.name
            file_type = original_file_name.split(".")[-1].lower()
            is_video = file_type in VideoTypes

            is_file_valid, errors = validate_file_type_and_size(file, allowed_types, max_file_size, max_video_size)
            if not is_file_valid:
                return format_error_response(status.HTTP_400_BAD_REQUEST, errors[0])

            if is_video:
                if videos_count.get(attachment_type, 0) >= max_no_of_videos:
                    return format_error_response(status.HTTP_400_BAD_REQUEST, f'Max {max_no_of_videos} video(s) allowed against "{doc_name}".')
            elif docs_count.get(attachment_type, 0) >= max_no_of_files:
                return format_error_response(status.HTTP_400_BAD_REQUEST, f'Max {max_no_of_files} files(s) allowed against "{doc_name}".')

            clean_file_name = clean_filename(original_file_name, add_timestamp=True)
            file_path = '{}/{}/{}/{}'.format(
                settings.ENVIRONMENT_ENV,
                SAASModules.VENDOR_ONBOARDING.name.lower(),
                request.company_id,
                clean_file_name
            )

            s3_path, s3_error = upload_to_s3(file, file_path)
            if s3_error:
                return format_error_response(status.HTTP_400_BAD_REQUEST, s3_error)

            new_attachment = AttachmentSchema(**{
                'file_name': original_file_name,
                'file_size': file.size,
                'file_type': file_type,
                'type': attachment_type,
                'url': s3_path,
                'created_by_id': request.user_id,
                'created_by': request.user_name
            }).model_dump()
            new_attachments.append(new_attachment)

        query = {
            'id': form_doc['id'],
            'company_id': request.company_id,
            'company_type': CompanyType.PROVIDER.value
        }

        set_query = {'updated_on': request.now}
        if not existing_attachments:
            set_query['attachments'] = new_attachments
            push_query = {}
        else:
            push_query = {'attachments': {'$each': new_attachments}}

        updated_doc = db.update(NetworkDBColls.ONBOARDING_FORMS, query, set_query=set_query, push_query=push_query, upsert=True, find_one_and_update=True)
        response_data = {
            'attachments': updated_doc['attachments']
        }
        return format_response(status.HTTP_200_OK, response_data, 'Attachment(s) uploaded successfully.')

    @validate_data_access(['onboarding_form'])
    def delete(self, request, *args, **kwargs):
        attachment_id = kwargs['attachment_id']
        form_doc = request.form_doc

        if form_doc['form_status_id'] in [OBFormStatus.REVIEW.value, OBFormStatus.APPROVED.value]:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Form has already been submitted.')

        try:
            attachment = [x for x in form_doc['attachments'] if x['id'] == attachment_id][0]
        except (KeyError, IndexError):
            attachment = {}

        if not attachment:
            return format_error_response(status.HTTP_404_NOT_FOUND, 'Attachment not found.')

        error = delete_from_s3(file_url=attachment['url'])

        delete_entry = (not error) or ('does not exist' in error.lower())
        if not delete_entry:
            return format_error_response(status.HTTP_400_BAD_REQUEST, error)

        db = MongoUtility()

        query = {
            'id': form_doc['id'],
            'company_id': request.company_id,
            'company_type': CompanyType.PROVIDER.value
        }
        pull_query = {'attachments': {'id': attachment_id}}
        set_query = {'updated_on': request.now}

        db.update(NetworkDBColls.ONBOARDING_FORMS, query, set_query=set_query, pull_query=pull_query)

        return format_response(status.HTTP_200_OK, {}, 'Attachment deleted successfully.')


class SubmitOnboardingFormAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateProviderOrAccessToken, )
    permissions = [
        (Permissions.VendorOnboarding.UPDATE, ['POST']),
    ]

    @validate_data_access(['onboarding_form'])
    def post(self, request, *args, **kwargs):
        form_doc = request.form_doc

        if form_doc['form_status_id'] in [OBFormStatus.REVIEW.value, OBFormStatus.APPROVED.value]:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Form has already been submitted.')

        form_doc['is_tnc_accepted'] = request.data.get('is_tnc_accepted', False)
        doc_types = Memoization.get_dropdown_data(AdminDBColls.ONBOARDING_DOCUMENT_TYPES)

        try:
            validated_data = OnboardingFormSchema(**form_doc).model_dump()
        except ValidationError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)

        if not validated_data['is_tnc_accepted']:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Please read and accept the Terms & Conditions.')

        db = MongoUtility()

        # check if all mandatory documents are uploaded
        documents_map = {x['type']: x for x in form_doc['attachments']}
        for document in doc_types:
            is_mandatory = document['is_required']
            if (not is_mandatory) and document.get('depends_on'):
                is_mandatory = form_doc[document['depends_on']]

            if is_mandatory and (document['id'] not in documents_map):
                return format_error_response(status.HTTP_400_BAD_REQUEST, f"Please upload required document - {document['name']}")

        form_status = OBFormStatus.REVIEW
        query = {
            'id': form_doc['id'],
            'company_id': request.company_id,
            'company_type': CompanyType.PROVIDER.value
        }
        update_query = {
            'form_status': form_status.name,
            'form_status_id': form_status.value,
            'tnc_accepted_on': request.now,
            'updated_on': request.now
        }
        db.update(NetworkDBColls.ONBOARDING_FORMS, query, update_query)
        return format_response(status.HTTP_200_OK, {}, "Success")
