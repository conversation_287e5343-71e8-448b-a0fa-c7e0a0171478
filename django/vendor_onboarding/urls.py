from django.urls import path
from .form_views import (
    ListOnboardingFormsAPI,
    CountOnboardingFormsAPI,
    GetOnboardingFormDataAPI,
    OnboardCompanyDataAPI,
    OnboardServiceDataAPI,
    OnboardDocumentsAPI,
    SubmitOnboardingFormAPI,
)
from .form_review_views import (
    VerifySectionAPI,
    ListRemarksAPI,
    RemarkCRUDAPI,
    ReSendOnboardingFormAPI,
    ApproveOnboardingFormAPI
)
from .views import SendOnboardingFormAPI

urlpatterns = [
    path('onboard/forms/listing/', ListOnboardingFormsAPI.as_view(), name='list_onboarding_forms'),
    path('onboard/forms/counts/', CountOnboardingFormsAPI.as_view(), name='count_onboarding_forms'),
    path('onboard/<str:company_id>/form/send/', SendOnboardingFormAPI.as_view(), name='send_onboarding_form'),
    path('onboard/<str:company_id>/form/', GetOnboardingFormDataAPI.as_view(), name='onboarding_form_data'),
    path('onboard/<str:company_id>/form/<str:form_id>/company-data/', OnboardCompanyDataAPI.as_view(), name='onboard_company_data'),
    path('onboard/<str:company_id>/form/<str:form_id>/service-data/', OnboardServiceDataAPI.as_view(), name='onboard_service_data'),
    path('onboard/<str:company_id>/form/<str:form_id>/documents/', OnboardDocumentsAPI.as_view(), name='upload_onboarding_documents'),
    path('onboard/<str:company_id>/form/<str:form_id>/document/<str:attachment_id>/', OnboardDocumentsAPI.as_view(), name='get_delete_onboarding_document'),
    path('onboard/<str:company_id>/form/<str:form_id>/submit/', SubmitOnboardingFormAPI.as_view(), name='submit_onboarding_form'),
    path('onboard/<str:company_id>/form/<str:form_id>/verification/', VerifySectionAPI.as_view(), name='verify_section'),
    path('onboard/<str:company_id>/form/<str:form_id>/remarks/listing/', ListRemarksAPI.as_view(), name='list_ob_form_remarks'),
    path('onboard/<str:company_id>/form/<str:form_id>/remark/', RemarkCRUDAPI.as_view(), name='add_ob_form_remark'),
    path('onboard/<str:company_id>/form/<str:form_id>/remark/<str:remark_id>/', RemarkCRUDAPI.as_view(), name='update_delete_ob_form_remark'),
    path('onboard/<str:company_id>/form/<str:form_id>/resend/', ReSendOnboardingFormAPI.as_view(), name='resend_ob_form_for_corrections'),
    path('onboard/<str:company_id>/form/<str:form_id>/approve/', ApproveOnboardingFormAPI.as_view(), name='approve_ob_form'),
]
