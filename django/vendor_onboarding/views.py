import logging
from pydantic import ValidationError
from rest_framework import status
from authn import (
    PermissionedAPIView,
    AuthenticateSeeker,
    Permissions
)
from authn.decorators import validate_data_access
from utils import (
    format_error_response,
    format_response,
    ConfigError
)
from utils.constants import (
    NetworkDBColls, OBFormStatus,
    EmailType,
)
from utils.mongo import MongoUtility
from schema import OnboardingFormSchema
from .onboarding_utils import send_onboarding_form_email

logger = logging.getLogger('application')


class SendOnboardingFormAPI(PermissionedAPIView):
    authentication_classes = (AuthenticateSeeker, )
    permissions = [
        (Permissions.VendorOnboarding.SEND_FORM, ['POST']),
    ]

    @validate_data_access(['vendor_company'])
    def post(self, request, *args, **kwargs):
        company_doc = request.company_doc

        db = MongoUtility()

        vendor_doc = {
            'id': company_doc['id'],
            'name': company_doc['name'],
            'email': company_doc['email'] or company_doc['spoc_email'],
        }

        resend_active_form = request.data.get('resend', False)
        email_type = request.data.get('email_type', EmailType.NEW_VENDOR_OB_FORM)
        is_new_form = False

        active_form_status = [
            OBFormStatus.PENDING.value,
            OBFormStatus.DRAFT.value,
            OBFormStatus.REVIEW.value,
        ]

        query = {
            'company_id': vendor_doc['id'],
            'form_status_id': {'$in': active_form_status}
        }
        active_form = db.find(NetworkDBColls.ONBOARDING_FORMS, query, find_one=True)
        if active_form:
            if not resend_active_form:
                return format_error_response(
                    status.HTTP_400_BAD_REQUEST,
                    "Onboarding is already in progress for the selected vendor."
                )
        else:
            DynamicModel = OnboardingFormSchema.configure_mandatory(strict=False)
            is_new_form = True

            try:
                active_form = DynamicModel(
                    seeker_id=request.company_id,
                    company_id=vendor_doc['id'],
                    name=vendor_doc['name'],
                    email=vendor_doc['email']
                ).model_dump()
            except ValidationError as e:
                return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)

        if is_new_form:
            db.insert(NetworkDBColls.ONBOARDING_FORMS, [active_form])

        try:
            send_onboarding_form_email(
                form_id=active_form['id'],
                vendor_id=active_form['company_id'],
                vendor_name=active_form['name'],
                vendor_email=active_form['email'] or active_form['spoc_email'],
                email_type=email_type,
            )
        except ConfigError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, str(e))
        except KeyError:
            return format_error_response(status.HTTP_400_BAD_REQUEST, "Improperly configured onboarding email config.")

        return format_response(status.HTTP_200_OK, {'id': active_form['id']}, "Success")
