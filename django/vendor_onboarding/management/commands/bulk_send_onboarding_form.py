import logging
from django.core.management.base import BaseCommand, CommandError
from utils.constants import (
    AdminDBColls, NetworkDBColls,
    EmailType, CompanyType,
    SAASModules
)
from utils import fetch_response, get_traceback, DateUtil
from utils.mongo import MongoUtility

logger = logging.getLogger('application')


# python manage.py bulk_send_onboarding_form --token "authtoken"

class Command(BaseCommand):

    def add_arguments(self, parser):
        parser.add_argument('--token')
        parser.add_argument('--created_after')

    def handle(self, *args, **options):
        token = options.get('token')
        created_after = options.get('created_after')
        if not token:
            raise CommandError("Missing auth token")

        if not created_after:
            raise CommandError("Missing created_after date: YYYY-MM-DD format")

        admin_db = MongoUtility(module_id=SAASModules.ADMIN.value)
        network_db = MongoUtility()

        sent_emails = []
        query = {'type': EmailType.OLD_VENDOR_OB_FORM, 'created_on': {'$gt': DateUtil.convert_to_utc(created_after)}}
        for item in admin_db.find(AdminDBColls.EMAIL_LOGS, query):
            sent_emails.extend(item['receivers'])

        query = {
            'company_type': CompanyType.PROVIDER.value,
            'is_active': True,
            'teg_id': {'$exists': True},
            "$and": [{'email': {'$nin': sent_emails}}, {'spoc_email': {'$nin': sent_emails}}]
        }

        vendors = admin_db.find(AdminDBColls.COMPANIES, query)
        total = vendors.count()

        bounced_emails = admin_db.distinct(AdminDBColls.BOUNCED_EMAILS, 'email', {})

        for i, vendor in enumerate(vendors, 1):
            email = vendor['email']
            if email in bounced_emails:
                logger.warning(f"[{i}/{total}][{vendor['id']}] Vendor email in bounce list. Skipping: {vendor['name']}")
                continue

            obj = {'id': vendor['id'], 'email': email}
            logger.info(f"[{i}/{total}][{vendor['id']}] Initiating onboarding for vendor: {vendor['name']}")

            try:
                base_url = 'http://localhost:5000/network'
                endpoint = f"/api/v1/vendor-onboarding/onboard/{vendor['id']}/form/send/"
                api_url = base_url + endpoint
                headers = {'token': token}
                payload = {
                    "resend": True,
                    "email_type": EmailType.OLD_VENDOR_OB_FORM
                }
                response = fetch_response(api_url, headers=headers, method='POST', payload=payload)
                status_code = response.status_code

                if (status_code == 200):
                    self.stdout.write(self.style.SUCCESS(f"Onboarding form sent to: {vendor['name']}"))
                else:
                    obj['error'] = response.text

            except Exception as e:
                obj['error'] = get_traceback(e)

            if obj.get('error'):
                logger.error(obj['error'])
                network_db.insert(NetworkDBColls.OB_FORM_ERRORS, [obj])

        self.stdout.write(self.style.SUCCESS('Bulk send onboarding form process completed...'))
