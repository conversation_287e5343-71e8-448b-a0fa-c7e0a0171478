import re
from pydantic import (
    BaseModel, EmailStr,
    Field, model_validator,
    field_validator, HttpUrl,
)
from typing import (
    Literal, Type, Self
)
from datetime import datetime
from utils.constants import (
    CompanyType, Currency,
    ServiceCategory, OBFormStatus,
    OBFormSections
)
from utils import DateUtil, make_optional
from schema import (
    TurnoverModel,
    AdminModel,
    GSTModel,
    AddressModel,
    RegionalOfficeModel,
    BranchOfficeModel,
    CustomerModel,
    MSMEModel,
    BankDetailsModel,
    ServiceCategoryModel,
    OperationalAreaModel,
    BusinessSegmentModel,
    ServicesModel,
    VehicleModel,
)

PAN_REGEX = r'^[A-Z]{5}[0-9]{4}[A-Z]{1}$'


class ListOBFormsParamsValidator(BaseModel):
    form_status_id: int = Field(..., in_=[x.name for x in OBFormStatus])
    limit: int = Field(default=20, ge=20, le=100)
    offset: int = Field(default=0, ge=0)
    sort_by: str = Field(default='created_on')
    sort_order: int = Field(default=-1, in_=[1, -1])
    search_term: str | None = Field(default=None, min_length=3, max_length=64)

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values


class GetFormDataParamsValidator(BaseModel):
    form_id: str | None = None
    preview_logo: bool | None = False
    preview_docs: bool | None = False


class OnboardCompanyDataPayloadValidator(AddressModel):
    # strict_validation: bool = Field(default=False, description='if true perform schema model validations before saving to DB')
    name: str = Field(..., min_length=3, max_length=64)
    company_type: Literal[CompanyType.PROVIDER.value] | None = CompanyType.PROVIDER.value
    logo: HttpUrl
    reg_type: str = Field(..., description='company registration type')
    reg_type_id: int
    website: HttpUrl | None = None
    pan: str = Field(..., min_length=10, max_length=10)
    year_of_reg: int | None = Field(default=None, gt=1950, le=datetime.now().year)
    email: EmailStr
    phone: int = Field(..., gt=**********, le=**********)
    ho_phone: int = Field(..., gt=**********, le=**********)
    rating: int | None = Field(default=None, ge=0)
    total_ratings: int | None = Field(default=None, ge=0)
    payment_currency: str | None = Field(default=Currency.INR.name, in_=[x.name for x in Currency])
    payment_currency_id: str | None = Field(default=Currency.INR.value, in_=[x.value for x in Currency])
    turnovers: list[TurnoverModel]
    same_as_company: bool = Field(default=False, description='to copy contact details from company details to spoc')
    spoc_name: str = Field(..., min_length=3, max_length=64)
    spoc_email: EmailStr
    spoc_phone: int = Field(..., gt=**********, le=**********)
    admin_details: AdminModel
    is_gst_registered: bool = False
    gst_details: list[GSTModel] | None = None
    regional_offices: list[RegionalOfficeModel] | None = None
    branch_offices: list[BranchOfficeModel] | None = None
    customers: list[CustomerModel] | None = None
    is_msme_registered: bool = False
    msme_details: MSMEModel | None = None
    bank_details: BankDetailsModel | None = None
    updated_on: int = Field(default_factory=DateUtil.get_current_timestamp)

    @classmethod
    def configure_mandatory(cls, strict: bool) -> Type[BaseModel]:
        """Dynamically modify required fields in a nested Pydantic model."""
        return make_optional(cls, strict)

    @field_validator('pan', mode='after')
    def validate_pan(cls, value: str) -> str:
        if value and not re.match(PAN_REGEX, value):
            raise ValueError("Invalid PAN. It must be a valid 10-character format.")
        return value

    @model_validator(mode='after')
    def reset_data(self) -> Self:
        if not self.is_gst_registered:
            self.gst_details = None

        if not self.is_msme_registered:
            self.msme_details = None
        return self


class OnboardServiceDataPayloadValidator(BaseModel):
    # strict_validation: bool = Field(default=False, description='if true perform schema model validations before saving to DB')
    service_categories: list[ServiceCategoryModel]
    operational_areas: list[OperationalAreaModel]
    business_segments: list[BusinessSegmentModel]
    services_offered: list[ServicesModel]
    vehicle_details: list[VehicleModel] | None = None

    @classmethod
    def configure_mandatory(cls, strict: bool) -> Type[BaseModel]:
        """Dynamically modify required fields in a nested Pydantic model."""
        return make_optional(cls, strict)

    @model_validator(mode='after')
    def validate_vehicle_details(self) -> Self:
        is_vehicle_details_required = False
        valid_service_categories = {
            ServiceCategory.TRANSPORTER.value: ServiceCategory.TRANSPORTER.name,
            ServiceCategory.FLEET_OWNER.value: ServiceCategory.FLEET_OWNER.name
        }
        for cat_model in self.service_categories:
            if cat_model.id in valid_service_categories:
                is_vehicle_details_required = True
                break

        if is_vehicle_details_required and not self.vehicle_details:
            raise ValueError(f"Vehicle Details are mandatory for service categories: {', '.join(list(valid_service_categories.values()))}")
        return self


class OBFormRemarksModel(BaseModel):
    section_id: OBFormSections
    remark: str = Field(..., min_length=3, max_length=255)
    created_on: int = Field(default_factory=DateUtil.get_current_timestamp)

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values


class OBFormRemarksPayloadValidator(BaseModel):
    remarks: list[OBFormRemarksModel]
