import logging
from celery import shared_task
from django.conf import settings
from config.celery_conf import app  # noqa. Ensure the Celery app is imported
from config.celery_conf import load_dynamic_schedule

logger = logging.getLogger('celery')


# @app.task
@shared_task
def refresh_celery_schedule():
    # app.conf.beat_schedule = load_dynamic_schedule()
    settings.CELERY_BEAT_SCHEDULE = load_dynamic_schedule()
    return "Celery Beat Schedule Updated!"
