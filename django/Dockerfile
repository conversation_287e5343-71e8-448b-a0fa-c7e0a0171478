FROM python:3.12

RUN ["apt-get", "update"]
RUN apt-get install -y ffmpeg

# Define build arguments
ARG BUILD_ENV

# Set environment variables based on build argument
ENV BUILD_ENV=${BUILD_ENV}

ENV PYTHONUNBUFFERED 1
ENV UV_SYSTEM_PYTHON=1

# Requirements have to be pulled and installed here, otherwise caching won't work
COPY ./requirements.txt /requirements.txt

# Install packages conditionally based on the build environment
RUN echo "BUILD_ENV is: $BUILD_ENV"

# Install uv for faster package management
RUN pip install uv==0.6.14

RUN if [ "$BUILD_ENV" = "local" ]; then \
        echo "Building in local environment"; \
        # Use this command in local for caching the downloaded packages
        # if the requirements.txt file is changed this makes sure that
        # the existing packages are used from cache as long as the version is not changed
        # pip install --cache-dir=/root/.cache --upgrade pip; \
        uv pip install --system --cache-dir=/root/.cache -r /requirements.txt; \
        uv pip install --system --cache-dir=/root/.cache ipdb; \
    else \
        echo "Building in non-local environment"; \
        # pip install --upgrade pip; \
        uv pip install --system -r /requirements.txt; \
    fi

RUN groupadd -r django \
    && useradd -r -g django django

WORKDIR /app
COPY . /app
RUN chown -R root /app

RUN mkdir -p /app/logs
RUN chown -R root /app/logs

RUN mkdir -p /app/media
RUN chown -R root /app/media

COPY ./entrypoint.sh /entrypoint.sh

RUN sed -i 's/\r//' /entrypoint.sh \
    && chmod +x /entrypoint.sh \
    && chown root /entrypoint.sh

EXPOSE 5000

ENTRYPOINT ["/entrypoint.sh"]
