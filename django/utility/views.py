import logging
from rest_framework.views import APIView
from rest_framework import status
from utils.constants import (
    ApiType, AdminDBColls
)
from utils import (
    format_response,
    format_error_response,
    Memoization, restructure_services_offered,
    get_search_results_from_google_geocode_api,
    get_search_results_from_google_places_api
)
from authn import AuthenticateAllOrAccessToken

logger = logging.getLogger('application')


class DropdownsAPIView(APIView):
    authentication_classes = (AuthenticateAllOrAccessToken, )

    def get(self, request, *args, **kwargs):
        params = request.GET

        try:
            dropdown_types = params.get('dropdown_type').split(',')
        except AttributeError:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                'Invalid dropdown_type recieved.',
            )

        data = {}
        for dropdown_type in dropdown_types:
            if hasattr(self, f'get_{dropdown_type}_dropdown'):
                dropdown_func = getattr(self, f'get_{dropdown_type}_dropdown')
            else:
                dropdown_func = self.get_dropdown_data

            data[dropdown_type] = dropdown_func(dropdown_type)

        return format_response(status.HTTP_200_OK, data, 'Success')

    def get_dropdown_data(self, dropdown_type):
        dropdown_coll = dropdown_type
        if dropdown_coll == AdminDBColls.SERVICES_OFFERED:
            data = Memoization.get_dropdown_data(dropdown_coll, sort=[('mode_type_id', 1), ('service_segment_id', 1), ('order', 1)])
            return restructure_services_offered(data)
        else:
            return Memoization.get_dropdown_data(dropdown_coll)


class SearchAddress(APIView):
    authentication_classes = (AuthenticateAllOrAccessToken, )

    def get(self, request, *args, **kwargs):
        params = request.GET
        search_term = params.get('search_term')
        lat = params.get('lat')
        lng = params.get('lng')
        place_id = params.get('place_id')
        location_type = params.get('type', 'establishment')
        api_type = int(params.get('api_type', ApiType.GOOGLE_PLACES_API.value))

        if api_type == ApiType.GOOGLE_GEOCODE_API.value:
            results = get_search_results_from_google_geocode_api(place_id, lat, lng, search_term)
        elif api_type == ApiType.GOOGLE_PLACES_API.value:
            results = get_search_results_from_google_places_api(search_term, location_type)
        else:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                'Invalid api_type recieved.'
            )
        return format_response(status.HTTP_200_OK, results, 'Success')
