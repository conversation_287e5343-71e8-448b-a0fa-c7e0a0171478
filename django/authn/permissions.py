from enum import Enum
from rest_framework.views import APIView
from rest_framework.permissions import BasePermission


class Permissions(object):

    class Admin:
        VIEW = 'admin.view'
        ADD_USERS = 'admin.users.create'
        UPDATE_USERS = 'admin.users.update'
        DELETE_USERS = 'admin.users.delete'

    class ExploreVendors:
        VIEW = 'explore_vendors.view'
        REGISTER = 'explore_vendors.register'
        ONBOARD = 'explore_vendors.onboard'

    class VendorOnboarding:
        VIEW = 'vendor_onboarding.view'
        CREATE = 'vendor_onboarding.create'
        UPDATE = 'vendor_onboarding.update'
        SEND_FORM = 'vendor_onboarding.send_form'
        REVIEW = 'vendor_onboarding.review'


class AccessPurpose(Enum):
    SUBMIT_VENDOR_ONBOARDING_FORM = 'submit_vendor_onboarding_form'


PURPOSE_PERMISSIONS = {
    AccessPurpose.SUBMIT_VENDOR_ONBOARDING_FORM.value: [
        Permissions.VendorOnboarding.VIEW,
        Permissions.VendorOnboarding.UPDATE,
    ]
}


class HasPermissions(BasePermission):

    def __init__(self, required_permissions):
        self.required_permissions = required_permissions

    def has_permission(self, request, view):
        # Skip permission checks for OPTIONS or super_admin, allowing it unconditionally
        if (request.method == 'OPTIONS') or request.is_super_admin:
            return True

        # If no specific permissions are mentioned for this method, deny access
        if not self.required_permissions:
            return False

        # Check if the user has all required permissions for the current method
        if hasattr(request, 'user_permissions'):
            return all(perm in request.user_permissions for perm in self.required_permissions)
        return False


class PermissionedAPIView(APIView):
    # Define permissions as a list of tuples with the format:
    # [('app_name.permission_code', ['METHOD1', 'METHOD2'])]
    permissions = []

    def get_permissions(self):
        # Filter permissions based on the request method
        required_permissions = [
            perm for perm, methods in self.permissions if self.request.method in methods
        ]
        # Return the permissions instance with the filtered permissions
        return [HasPermissions(required_permissions)]
