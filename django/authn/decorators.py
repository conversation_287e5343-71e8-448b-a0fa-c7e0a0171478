from functools import wraps
from pydantic import ValidationError
from rest_framework import status
from utils import format_error_response
from utils.mongo import MongoUtility
from utils.constants import (
    AdminDBColls,
    NetworkDBColls,
    CompanyType,
    SAASModules
)


def validate_query_params(validator_class):
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            try:
                query_params = request.GET.dict()
                validated_data = validator_class(**query_params)
                request.validated_params = validated_data
            except ValidationError as e:
                return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


def validate_request_payload(validator_class):
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            try:
                payload = request.data
                validated_data = validator_class(**payload)
                request.validated_payload = validated_data
            except ValidationError as e:
                return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


def validate_data_access(items: list[str]):
    class DataAccessValidators:

        @staticmethod
        def validate_access_to_vendor_company(request, *args, **kwargs):
            db = MongoUtility(module_id=SAASModules.ADMIN.value)

            query = {'id': kwargs['company_id'], 'company_type': CompanyType.PROVIDER.value}

            company_doc = db.find(AdminDBColls.COMPANIES, query, find_one=True)
            if (not company_doc) or (request.is_provider and company_doc['id'] != request.company_id):
                raise ValueError('Vendor Company not found.')

            request.company_doc = company_doc
            return True

        @staticmethod
        def validate_access_to_onboarding_form(request, *args, **kwargs):
            db = MongoUtility()

            query = {
                'id': kwargs['form_id'],
                'company_id': kwargs['company_id'],
                'company_type': CompanyType.PROVIDER.value
            }
            if request.is_seeker:
                query['seeker_id'] = request.company_id

            form_doc = db.find(NetworkDBColls.ONBOARDING_FORMS, query, find_one=True)

            if (not form_doc) or (request.is_provider and form_doc['company_id'] != request.company_id):
                raise ValueError('Form data not found.')

            request.form_doc = form_doc
            return True

        @staticmethod
        def validate_access_to_ob_form_remark(request, *args, **kwargs):
            db = MongoUtility()

            query = {
                'id': kwargs['remark_id'],
                'form_id': kwargs['form_id'],
                'provider_id': kwargs['company_id'],
                'seeker_id': request.company_id,
            }
            remark_doc = db.find(NetworkDBColls.ONBOARDING_FORM_REMARKS, query, find_one=True)

            if not remark_doc:
                raise ValueError('Form Remark not found.')

            request.remark_doc = remark_doc
            return True

    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(self, request, *args, **kwargs):
            for item in items:
                try:
                    getattr(DataAccessValidators, f'validate_access_to_{item}')(request, *args, **kwargs)
                except ValueError as e:
                    return format_error_response(status.HTTP_404_NOT_FOUND, str(e))

            return view_func(self, request, *args, **kwargs)
        return _wrapped_view
    return decorator
