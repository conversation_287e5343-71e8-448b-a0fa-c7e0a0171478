{% extends "base.html" %}

{% block email_content %}
<tr>
    <td style="padding:16px;font-size:12px">
        <style>
            .email-container p {
                color: #30343C;
                font-family: Verdana,arial,Helvetica,sans-serif;
                font-size: 12px;
                line-height: 1.6;
                margin: 8px 0;
            }
            .email-container h2 {
                color: #30343C;
                font-family: Verdana,arial,Helvetica,sans-serif;
                font-size: 16px;
                font-weight: 600;
                margin: 16px 0;
            }
            .login-link {
                background-color: #597EF7;
                color: white !important;
                text-decoration: none;
                padding: 10px 20px;
                border-radius: 4px;
                display: inline-block;
                margin: 10px 0;
                font-weight: bold;
            }
            .email-link {
                background-color: #e0f0ff;
                color: #597EF7;
                padding: 2px 4px;
                border-radius: 3px;
                text-decoration: none;
            }
            .website-link {
                color: #597EF7;
                text-decoration: none;
                font-weight: 600;
            }
        </style>

        <h2>Account Reviewed | Status - <strong style="color:{% if message.status == 'ACTIVE' %}green{% else %}red{% endif %};">{{ message.status }}</strong></h2>

        <p>Dear {{ message.company_name }},</p>

        {% if message.status == 'ACTIVE' %}
            <p>We're pleased to inform you that your company account for <strong>{{ message.company_name }}</strong> has been <strong style="color:green;">successfully verified</strong>.</p>
            <p>You can now log in and start using <a href="#" style="box-sizing:border-box;margin:0;padding:0;cursor:pointer;text-decoration:none;"><span style="box-sizing:border-box;margin:0;padding:0;color: #597EF7; font-size: 12px; font-family: Verdana,arial,Helvetica,sans-serif; font-weight: 600; line-height: 14px; word-wrap: break-word;">SCLEN.</span><span style="box-sizing:border-box;margin:0;padding:0;color: #50E3C2; font-size: 12px; font-family: Verdana,arial,Helvetica,sans-serif; font-weight: 600; line-height: 14px; word-wrap: break-word;">AI</span></a> to avail services/features.</p>
            <p><a href="{{ message.login_link }}" class="login-link">Login Now</a></p>
        {% elif message.status == 'REJECTED' %}
            <p>We regret to inform you that your company account for <strong>{{ message.company_name }}</strong> could not be verified at this time.</p>
            <p><strong>Reason:</strong> {{ message.remarks }}</p>
            <p>If you believe this was a mistake or need further assistance, feel free to reach out to our support team.</p>
            <p>Contact Support at: <a href="mailto:<EMAIL>" class="email-link"><EMAIL></a></p>
        {% endif %}

        <p>Thank you for choosing <a href="#" style="box-sizing:border-box;margin:0;padding:0;cursor:pointer;text-decoration:none;"><span style="box-sizing:border-box;margin:0;padding:0;color: #597EF7; font-size: 12px; font-family: Verdana,arial,Helvetica,sans-serif; font-weight: 600; line-height: 14px; word-wrap: break-word;">SCLEN.</span><span style="box-sizing:border-box;margin:0;padding:0;color: #50E3C2; font-size: 12px; font-family: Verdana,arial,Helvetica,sans-serif; font-weight: 600; line-height: 14px; word-wrap: break-word;">AI</span></a></p>
    </td>
</tr>
{% endblock %}
