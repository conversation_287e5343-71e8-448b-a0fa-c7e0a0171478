import re
import json
import logging
from urllib.parse import urlparse
from pydantic import ValidationError
from utils import DateUtil, Memoization, get_search_results_from_google_places_api
from utils.mongo import MongoUtility
from utils.constants import (
    AdminDBColls, NetworkDBColls,
    OBFormStatus, CompanyType,
    Currency, SAASModules
)
from vendor_onboarding.request_validators import PAN_REGEX
from schema import OnboardingFormSchema, BasicProviderCompanySchema, GSTIN_REGEX

logger = logging.getLogger('application')

# Prod
TEG_ADMIN_DB_URL = ""
TEG_ADMIN_DB_NAME = "tegadmin_prod"
IS_PROD_MIGRATION = True


class TEGAdminDBColls:
    PROVIDERS = 'providers'
    OB_VENDOR = 'onboarding_vendor'
    OB_VENDOR_DETAILS = 'onboarding_vendor_details'


def remove_non_ascii(s: str) -> str:
    string = (''.join(c for c in s if ord(c) < 128)).strip()
    return re.sub(r'\s+', ' ', string).strip()


def convert_to_dummy_email(email):
    if (not IS_PROD_MIGRATION) and email and ('@' in email):
        email = f"{email.strip().split('@')[0]}@yopmail.com"
    return email


def validate_with_regex(pattern, string):
    string = (string or '').strip()
    if not string:
        return

    res = re.match(pattern, string)
    if res:
        return string
    logger.warning(f'Invalid string format recieved for {pattern}: {string}')


def correct_state_name(name, states_list):
    mapping = {
        'Chattisgarh': 'CG',
        'Maharastra': 'MH',
        'Daman & Diu': 'DD',
        'West Bangal': 'WB',
        'Orissa': 'OR',
        'Pondicherry': 'PY',
        'Mizorum': 'MZ',
        'J&K': 'JK',
        'Harayana': 'HR',
        'Uttaranchal': 'UK',
    }
    if name not in mapping:
        logger.error(f'State Name mismatch: {name}')
        return

    for item in states_list:
        if mapping[name] == item['id']:
            return item['name']


def is_valid_url(url: str) -> bool:
    try:
        # Parse the URL
        parsed = urlparse(url)

        # Check if scheme and netloc are present
        if not parsed.scheme or not parsed.netloc:
            return False

        # Define a regex pattern for a valid URL
        url_pattern = re.compile(
            r"^(https?|ftp)://"  # Scheme (http, https, ftp)
            r"(([A-Za-z0-9-]+\.)+[A-Za-z]{2,63})"  # Domain
            r"(/.*)?$"  # Path
        )

        return bool(url_pattern.match(url))
    except Exception:
        return False  # Invalid format


def fix_url(url: str, default_scheme="https"):
    if url:
        url = url.strip()
        parsed = urlparse(url, scheme=default_scheme)

        # Ensure scheme is included
        if not parsed.scheme:
            url = f"{default_scheme}://{url}"

        # Ensure it has a valid domain format
        if not urlparse(url).netloc:
            url = f"{default_scheme}://{url}"

        if not is_valid_url(url):
            logger.warning(f'Invalid URL: {url}')
            url = None
    return url


# Function to correct UOM based on number of digits
def convert_to_crores(turnover_value: str, turnover_uom: str):
    value = float(turnover_value)
    num_digits = len(str(int(value)))  # Count digits
    corrected_uom = "Crores"

    # Convert to Crores
    if num_digits >= 6:
        value /= 1_00_00_000
    else:
        if turnover_uom == 'Lakhs':
            value /= 100

    return round(value, 2), corrected_uom  # Round to 2 decimal places


# Function to classify turnover into a bracket
def classify_turnover(turnover_brackets: list, turnover_value: str, turnover_uom: str):
    corrected_value, corrected_uom = convert_to_crores(turnover_value, turnover_uom)

    # Match bracket with corrected value and unit
    for bracket in turnover_brackets:
        lower, upper = bracket['range']
        if lower <= corrected_value < upper:
            item = {
                'bracket_id': bracket['id'],
                'bracket': bracket['name'],
            }
            return item, corrected_value
    return None, None  # If no bracket found


# Function to extract range from category name
def parse_turnover_range(name):
    numbers = re.findall(r"\d+", name)  # Extract numbers
    words = name.split()  # Split words

    # Unit multipliers
    multipliers = {"Lakhs": 0.01, "Crores": 1, "Crore": 1}

    # Convert extracted numbers to integer values
    values = [int(num) for num in numbers]

    # Determine the unit (Lakhs/Crores)
    unit = "Lakhs" if "Lakhs" in words else "Crores" if "Crores" in words or "Crore" in words else None

    # If "Under X Crores"
    if "Under" in words:
        return (0, values[0] * multipliers[unit])

    # If "X - Y Crores"
    elif "-" in name:
        parts = name.split("-")
        first_part, second_part = parts[0].strip(), parts[1].strip()

        # Determine unit for first number
        first_unit = "Lakhs" if "Lakhs" in first_part else "Crores"
        first_value = values[0] * multipliers[first_unit]

        # Determine unit for second number
        second_unit = "Lakhs" if "Lakhs" in second_part else "Crores"
        second_value = values[1] * multipliers[second_unit]

        return (first_value, second_value)

    # If "Above X Crores"
    elif "Above" in words:
        return (values[0] * multipliers[unit], float("inf"))

    return None  # In case of an unknown format


class ImportVendors:

    def __init__(self):
        self.teg_db = MongoUtility(TEG_ADMIN_DB_URL, TEG_ADMIN_DB_NAME)
        self.saas_admin_db = MongoUtility(module_id=SAASModules.ADMIN.value)
        self.saas_network_db = MongoUtility()
        self.now = DateUtil.get_current_timestamp()

    def get_teg_ob_vendors(self, limit=320):
        ob_vendors_map = {}
        offset = 0

        while True:
            df = {'type': 0, 'charges': 0, 'scopes': 0, 'services_offered': 0, 'documents': 0}
            ob_vendors = self.teg_db.find(TEGAdminDBColls.OB_VENDOR, {}, df).limit(limit).skip(offset)
            ob_vendor_ids = []
            for x in ob_vendors:
                ob_vendor_ids.append(x['ob_vendor_id'])
                ob_vendors_map[x['ob_vendor_id']] = x

            total = len(ob_vendor_ids)
            if total <= 0:
                break

            logger.info(f'Fetching OBVendors data | chunk: {int(offset / limit) + 1} | limit: {limit} | offset: {offset} | total: {total}')

            df = {'ob_vendor_id': 1, 'operationalArea': 1}
            ob_vendor_details = self.teg_db.find(TEGAdminDBColls.OB_VENDOR_DETAILS, {'ob_vendor_id': {'$in': ob_vendor_ids}}, df)
            for doc in ob_vendor_details:
                ob_vendors_map[doc['ob_vendor_id']]['details'] = doc

            offset += limit

        return list(ob_vendors_map.values())

    def get_turnover_brackets(self):
        turnover_brackets = []
        for item in self.saas_admin_db.find(AdminDBColls.TURNOVER_BRACKETS, {}, sort=[('order', 1)]):
            item['range'] = parse_turnover_range(item['name'])
            turnover_brackets.append(item)
        return turnover_brackets

    def map_company_details(self, data):
        try:
            currency = Currency(data.get('currency') or 'INR')
        except ValueError as e:
            logger.warning(f'{e}')
            currency = Currency.INR

        contact_no = data.get('contact_no') or {}
        if isinstance(contact_no, dict):
            phone_no = contact_no.get('phone_no') or None
        elif isinstance(contact_no, str):
            phone_no = contact_no or None
        else:
            phone_no = None

        # logo = data.get('company_logo') or None

        return {
            'id': data['ob_vendor_id'],
            'form_status_id': OBFormStatus.PENDING.value,
            'form_status': OBFormStatus.PENDING.name,
            'company_id': data['ob_vendor_id'],
            'name': data.get('vendor_company_name') or None,
            'company_type': CompanyType.PROVIDER.value,
            'reg_type': None,
            'reg_type_id': None,
            'website': fix_url(data.get('company_website') or None),
            'logo': None,
            'year_of_reg': data.get('registration_year') or None,
            'pan': validate_with_regex(PAN_REGEX, data.get('pan_no')),
            'email': None,
            'phone': None,
            'ho_phone': None,
            'payment_currency': currency.name,
            'payment_currency_id': currency.value,
            'spoc_name': data.get('contact_person_name') or None,
            'spoc_email': convert_to_dummy_email(data['email'] or None),
            'spoc_phone': phone_no,
            'is_tnc_accepted': False,
            'tnc_accepted_on': None,
            'last_seen_on': None,
            'created_on': self.now,
            'updated_on': self.now,
        }

    def map_turnovers(self, data):
        turnovers = []
        turnover_details = data.get('turnover_details') or []
        for item in turnover_details:
            year, amount, uom = item.get('turnover_year') or None, item.get('turnoverValue') or 0, item.get('turnover_uom')
            # logger.info(f'Turnover | Year: {year} | Amount: {amount} | UOM: {uom}')

            if not float(amount):
                continue

            bracket, corrected_value = classify_turnover(self.turnover_brackets, amount, uom)
            # logger.info(f'New Amount: {corrected_value} | Bracket: ({bracket["bracket"]})\n')

            obj = {
                'year': year,
                **bracket
            }
            if any(obj.values()):
                turnovers.append(obj)

        return {
            'turnovers': turnovers or None
        }

    def map_admin_details(self, data):
        try:
            user_details = data.get('userList', [])[0]
        except (AttributeError, IndexError):
            return {}

        admin_details = {
            'user_name': user_details.get('admin_name') or None,
            'email': convert_to_dummy_email(user_details.get('admin_email') or None),
            'phone': user_details.get('admin_contact') or None,
        }
        if any(admin_details.values()):
            return {'admin_details': admin_details}
        return {}

    def map_gst_details(self, data):
        gst_details = []
        for item in (data.get('gstin_details') or []):
            gstin = validate_with_regex(GSTIN_REGEX, item.get('gstin'))
            if gstin:
                gst_details.append({
                    'gstin': gstin
                })
        return {'gst_details': gst_details}

    def map_customers(self, data):
        customers = []
        for item in (data.get('customerList') or []):
            obj = {
                'name': item.get('customer_name') or None,
                'service_provided': item.get('service_provided') or None,
                'product_handled': item.get('product_handled') or None,
            }
            if any(obj.values()):
                customers.append(obj)
        return {'customers': customers}

    def map_bank_details(self, data):
        bank_details = {
            'bank_name': data.get('bank_name') or None,
            'branch_name': data.get('branch_name') or None,
            'ifsc_code': None,
            'acc_holder_name': data.get('account_name') or None,
            'acc_no': data.get('account_number') or None,
        }
        if any(bank_details.values()):
            return {'bank_details': bank_details}
        return {}

    def map_operational_areas(self, data):
        operational_areas = []
        states_map = {x['name']: x['id'] for x in self.states_list}

        for area in (data.get('details', {}).get('operationalArea') or []):
            # skip pan-india option
            if area.get('oz_id') == 1:
                continue

            for entry in (area.get('details') or []):
                state_name = entry.get('state')
                if state_name and state_name not in states_map:
                    state_name = correct_state_name(state_name, self.states_list)

                if entry.get('is_enable', False) and (state_name in states_map):
                    operational_areas.append({
                        'id': states_map[state_name],
                        'name': state_name,
                    })
        return {'operational_areas': operational_areas}

    def process_teg_ob_vendors(self, ob_vendors, teg_providers_map):
        total = len(ob_vendors)
        ob_forms = []

        # pre-requisite data
        self.turnover_brackets = self.get_turnover_brackets()
        self.states_list = Memoization.get_dropdown_data(AdminDBColls.STATES_LIST)

        mapping_sections = [
            'company_details',
            'turnovers',
            'admin_details',
            'gst_details',
            'customers',
            'bank_details',
            'operational_areas',
        ]
        for i, vendor in enumerate(ob_vendors, 1):
            form_id = vendor['ob_vendor_id']
            company_id = vendor['company_id']
            ob_form_data = {}

            logger.info(f'[{i}/{total}] Processing | FormID: {form_id} | CompanyID: {company_id}')

            for section in mapping_sections:
                data = getattr(self, f'map_{section}')(vendor)
                ob_form_data.update(**data)

            DynamicModel = OnboardingFormSchema.configure_mandatory(strict=False)
            try:
                ob_form_doc = DynamicModel(**ob_form_data).model_dump()
            except ValidationError as e:
                logger.error(f'Error validating data: {e}')
                continue

            spoc_email = ob_form_doc['spoc_email']
            admin_email = (ob_form_doc.get('admin_details') or {}).get('email')

            if spoc_email in teg_providers_map:
                saas_provider_id = teg_providers_map[spoc_email]
            elif admin_email in teg_providers_map:
                saas_provider_id = teg_providers_map[admin_email]
            else:
                logger.warning(f'Email not found: {spoc_email} | {admin_email}')
                saas_provider_id = None

            if saas_provider_id:
                # company = self.saas_admin_db.find(AdminDBColls.COMPANIES, {'saas_provider_id': teg_id}, {'name': 1, 'id': 1, 'teg_id': 1}, find_one=True)
                ob_form_doc['company_id'] = saas_provider_id
                ob_forms.append(ob_form_doc)
        return ob_forms

    def import_teg_providers(self, delete_existing=False, insert_new=False):
        if delete_existing:
            res = self.saas_admin_db.delete(AdminDBColls.COMPANIES, {'teg_id': {'$exists': True}}, delete_many=True)
            logger.info(f'Deleted {res.deleted_count} existing teg providers')

        # with open('/app/scripts/teg_transporters_validated.json', 'r') as file:
        #     data = json.load(file)
        # providers_to_be_imported = [x['email'] for x in data if x['email_is_valid']]

        query = {'company_type': CompanyType.PROVIDER.value, 'teg_id': {'$ne': None}}
        existing_provider_ids = self.saas_admin_db.distinct(AdminDBColls.COMPANIES, 'teg_id', query)

        df = {
            'company_id': 1,
            'company_name': 1,
            'company_email': 1,
            'email': 1,
            'pan_no': 1,
            'contact': 1,
            'created': 1,
            'updated_on': 1
        }
        query = {
            # 'is_active': True,
            'is_admin': True,
        }
        providers = self.teg_db.find(TEGAdminDBColls.PROVIDERS, query, df, sort=[('email', 1), ('created', 1), ('updated_on', 1)])
        total = providers.count()

        actual_count = 1
        teg_providers = []
        existing_emails, duplicates_emails = [], []
        for i, x in enumerate(providers, 1):
            company_email = remove_non_ascii(x['company_email'] if '@' in (x.get('company_email') or '') else '').lower()
            admin_email = remove_non_ascii(x['email']).lower()
            email = convert_to_dummy_email(company_email or admin_email)
            pan = validate_with_regex(PAN_REGEX, x.get('pan_no') or '')
            company_name = remove_non_ascii(x['company_name']).title()

            try:
                if (x['company_id'] in existing_provider_ids):
                    logger.warning(f"[{actual_count}/{i}/{total}] Already Exists | {x['company_id']} | {company_name} | {email}")
                    continue
            except KeyError:
                logger.warning('Missing "company_id" in teg providers collection doc.')
                continue

            # if (company_email not in providers_to_be_imported) and (admin_email not in providers_to_be_imported):
            #     continue

            try:
                phone = x.get('contact') or None
                if phone:
                    phone = int(phone)
            except (TypeError, ValueError):
                phone = None

            logger.info(f"[{actual_count}/{i}/{total}] Copying provider | {x['company_id']} | {company_name} | {email}")
            actual_count += 1

            if email and (email not in existing_emails):
                existing_emails.append(email)
            else:
                duplicates_emails.append(email)

            try:
                doc = BasicProviderCompanySchema(**{
                    'name': company_name,
                    'email': email,
                    'pan': pan,
                    'phone': phone,
                    'email': email,
                    'teg_id': x['company_id'],
                }).model_dump()
            except ValidationError as e:
                logger.error(f'Error validating data: {e}')
                continue

            teg_providers.append(doc)

        logger.warning(f'Duplicate Emails: {duplicates_emails}')
        for x in teg_providers:
            if x['email'] in duplicates_emails:
                x['is_duplicate'] = True

        if insert_new and teg_providers:
            logger.info(f'Bulk Inserting {len(teg_providers)} providers')
            self.saas_admin_db.insert(AdminDBColls.COMPANIES, teg_providers, insert_many=True)

    def copy_form_data_to_companies(self):
        teg_ids = self.saas_admin_db.distinct(AdminDBColls.COMPANIES, 'id', {'teg_id': {'$ne': None}})

        ob_forms = self.saas_network_db.find(NetworkDBColls.ONBOARDING_FORMS, {'company_id': {'$in': teg_ids}, 'created_on': {'$gt': 1751414400000}})
        total_forms = ob_forms.count()

        for i, form in enumerate(ob_forms, 1):
            vendor_id = form['company_id']
            vendor_company = form.copy()

            unwanted_keys = [
                'id', 'form_status_id', 'form_status', 'company_id',
                'seeker_id', 'is_tnc_accepted', 'tnc_accepted_on',
                'last_seen_on', 'created_on', 'updated_on',
            ]
            for key in unwanted_keys:
                try:
                    del vendor_company[key]
                except KeyError:
                    pass

            try:
                geo_result = {}
                if vendor_company['name']:
                    geo_result = get_search_results_from_google_places_api(vendor_company['name'])['results'][0]
            except IndexError:
                logger.error('Location not found')

            logger.info(f"[{i}/{total_forms}][{vendor_id}] | Name: {vendor_company['name']} | Rating: {geo_result.get('rating')} | Total Ratings: {geo_result.get('total_ratings')}")

            vendor_company.update({
                'rating': geo_result.get('rating'),
                'total_ratings': geo_result.get('total_ratings'),
                'updated_on': self.now
            })

            self.saas_admin_db.update(AdminDBColls.COMPANIES, {'id': vendor_id, 'company_type': CompanyType.PROVIDER.value}, vendor_company, upsert=True)

    def run(self, update=False, import_providers=False, skip=False):
        if import_providers:
            self.import_teg_providers()

        teg_providers_map = {}
        if not skip:
            logger.info('Fetching teg provider company emails')
            company_emails = [x.strip().lower() for x in self.teg_db.distinct(TEGAdminDBColls.OB_VENDOR, 'email', {}) if (x or '').strip()]

            logger.info('Fetching teg provider admin emails')
            admin_emails = [x.strip().lower() for x in self.teg_db.distinct(TEGAdminDBColls.OB_VENDOR, 'userList.0.admin_email', {}) if (x or '').strip()]

            ob_vendor_emails = list(set(company_emails) | set(admin_emails))

            query = {
                "$expr": {
                    "$in": [{"$toLower": "$email"}, ob_vendor_emails]
                },
                'is_active': True,
                'company_type': CompanyType.PROVIDER.value,
            }
            # teg_providers = self.teg_db.find(TEGAdminDBColls.PROVIDERS, query, {'email': 1, 'company_id': 1})
            teg_providers = self.saas_admin_db.find(AdminDBColls.COMPANIES, query, {'email': 1, 'id': 1})

            logger.info('Creating teg providers email and company_id map')
            teg_providers_map = {convert_to_dummy_email(x['email'].strip().lower()): x['id'] for x in teg_providers}

        logger.info('Fetching teg OB Vendors')
        teg_ob_vendors = self.get_teg_ob_vendors() or []

        ob_forms = self.process_teg_ob_vendors(teg_ob_vendors, teg_providers_map)

        if update:
            total = len(ob_forms)
            for i, ob_form in enumerate(ob_forms, 1):
                form_id = ob_form['id']
                company_id, company_name = ob_form['company_id'], ob_form['name']

                exists = self.saas_network_db.find(NetworkDBColls.ONBOARDING_FORMS, {'id': form_id, 'company_id': company_id}, find_one=True)
                logger.info(f'[{i}/{total}] Exists: {bool(exists)} | FormID: {form_id} | CompanyID: {company_id} | CompanyName: {company_name}')
                self.saas_network_db.update(NetworkDBColls.ONBOARDING_FORMS, {'id': form_id, 'company_id': company_id}, ob_form, upsert=True)


# python manage.py runscript import_vendors --script-args 0 0
def run(update=False, import_providers=False):
    inst = ImportVendors()
    inst.import_teg_providers()
    # inst.run(bool(int(update)), bool(int(import_providers)))
    # inst.copy_form_data_to_companies()
