import logging
from django.conf import settings
from rest_framework.response import Response
from rest_framework.views import exception_handler
from utils.constants import ErrorMessages
from .common import get_traceback

logger = logging.getLogger('application')


class ErrorResponse(object):
    API_ERROR = 'api_error'
    AUTHENTICATION_ERROR = 'authentication_error'
    INVALID_REQUEST_ERROR = 'invalid_request_error'
    RATE_LIMIT_ERROR = 'rate_limit_error'
    VALIDATION_ERROR = 'validation_error'
    ACCESS_DENIED = 'access_denied'

    @staticmethod
    def get_response_obj(error_type, message, status=None):
        resp = {
            'type': error_type,
            'message': message
        }
        if status:
            resp['status'] = int(status)
        return resp


def format_response(status, data, message):
    response = {
        "status": status,
        "data": data,
        "message": message
    }
    return Response(data=response, status=status)


def format_error_response(status, error_message='', error=None, pydantic_error=None):
    errors_list = []
    if error:
        errors_list = [str(error)]
        if not isinstance(error, str):
            logger.error(f"{get_traceback(error)}")

    if pydantic_error:
        errors_list = pydantic_error.errors(include_context=False, include_url=False)
        if not error_message:
            msg = errors_list[0]['msg']
            try:
                key = '.'.join(map(str, errors_list[0]['loc']))
                error_message = f'{key}: {msg}'
            except IndexError:
                error_message = f'{msg}'

    if not settings.DEBUG_LOGS:
        errors_list = []

    response = {
        'status': status,
        'data': {
            'errors': errors_list
        },
        'message': error_message
    }
    return Response(data=response, status=status)


def custom_exception_handler(exc, context):
    # Call REST framework's default exception handler first,
    # to get the standard error response.
    response = exception_handler(exc, context)

    if not response:
        return response

    # Now add the HTTP status code to the response.
    response.data['status'] = response.status_code
    msg = response.data.get('message')
    if (msg == ErrorMessages.INVALID_TOKEN) or (response.status_code == 401):
        response.status_code = 401
        response.data.update({
            'status': 401,
            'data': response.data.get('data', {}),
            'message': ErrorMessages.INVALID_TOKEN
        })
    elif (msg == ErrorMessages.ACCESS_DENIED) or (response.status_code == 403):
        response.status_code = 403
        response.data.pop('detail', None)
        response.data.update({
            'status': 403,
            'data': response.data.get('data', {}),
            'message': ErrorMessages.ACCESS_DENIED
        })
    return response
