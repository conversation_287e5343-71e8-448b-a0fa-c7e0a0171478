import os
import io
import boto3
import urllib
import logging
from urllib.parse import urlparse
from django.conf import settings
from .common import get_traceback
from .constants import (
    ErrorLogTypes,
    LongMIMETypes,
    FILE_TYPE_BACKEND
)

logger = logging.getLogger('application')


def get_presigned_url(file_url, for_download=False, s3_bucket=settings.SAAS_S3_BUCKET, link_expiry=settings.PRESIGNED_LINK_EXPIRY):
    file_path = (urlparse(file_url or '', allow_fragments=False).path).strip('/')

    if file_path.startswith(s3_bucket):
        file_path = file_path.rsplit(s3_bucket, 1)[-1].strip('/')
    else:
        s3_bucket = file_path.split('/')[0]
        file_path = file_path.rsplit(s3_bucket, 1)[-1].strip('/')

    s3 = boto3.client(
        's3',
        aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
        aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
    )

    # Get the bucket location (region)
    region = s3.get_bucket_location(Bucket=s3_bucket)['LocationConstraint']
    if region is None:
        region = settings.REGION_NAME  # Default region for S3

    s3 = boto3.client(
        's3',
        region_name=region,
        aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
        aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
    )

    signed_url, error = None, None
    try:
        params = {'Bucket': s3_bucket, 'Key': urllib.parse.unquote_plus(file_path)}
        if for_download:
            # this gives a direct download link instead of previewing th file in the browser
            params['ResponseContentDisposition'] = 'attachment'

        signed_url = s3.generate_presigned_url(
            'get_object',
            Params=params,
            ExpiresIn=link_expiry
        )
    except Exception:
        error = 'Failed to generate link for the attachment.'
    return signed_url, error


def upload_to_s3(file, file_path, s3_bucket=settings.SAAS_S3_BUCKET, format_type=''):
    file_url, error = '', None
    file_name = None

    def get_content_type(file_name):
        try:
            format_type = file_name.rsplit('.', 1)[-1].lower()
        except (IndexError, AttributeError):
            format_type = None
        return FILE_TYPE_BACKEND.get(format_type, LongMIMETypes.BIN_OCTET_STREAM)

    try:
        s3 = boto3.client(
            's3',
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
        )

        # Get the bucket location (region)
        region = s3.get_bucket_location(Bucket=s3_bucket)['LocationConstraint']
        if region is None:
            region = settings.REGION_NAME  # Default region for S3

        # Construct the region host
        region_host = f's3.{region}.amazonaws.com'

        s3_path = file_path.strip('/')
        if isinstance(file, str):
            # Case when file is being uploaded directly from the disk
            file_name = file.rsplit("/", 1)[-1]
            content_type = get_content_type(file_name)

            # Open the local file as a file-like object
            with open(file, 'rb') as file_obj:
                # Upload the file-like object to S3
                s3.put_object(Body=file_obj, Bucket=s3_bucket, ContentType=content_type, Key=s3_path)
        else:
            file_name = file.name
            content_type = get_content_type(file_name)

            try:
                bytes_data = file.file.getvalue()
            except AttributeError:
                # when multiple files are uploaded at once
                file.file.file.seek(0)
                bytes_data = file.file.file.read()

            s3.put_object(Body=bytes_data, Bucket=s3_bucket, ContentType=content_type, Key=s3_path)

        # need to encode the file path as the link generated in S3 is encoded.
        file_url = 'https://{}/{}/{}'.format(
            region_host,
            s3_bucket,
            urllib.parse.quote_plus(s3_path, safe='/')
        )
    except Exception as e:
        logger.error("{} {}".format(ErrorLogTypes.S3_FILE_UPLOAD, get_traceback(e)))
        error = 'Error uploading the file ({}) [{}]'.format(file_name, e)
    return file_url, error


def upload_local_file_to_s3(file_path, s3_key, s3_bucket=settings.SAAS_S3_BUCKET, format_type=''):
    file_url, error = '', None
    try:
        file_name = file_path.rsplit("/", 1)[-1]

        if not format_type:
            try:
                format_type = file_name.rsplit(".", 1)[-1].lower()
            except (IndexError, AttributeError):
                pass

        s3 = boto3.client(
            's3',
            region_name=settings.REGION_NAME,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
        )

        s3_key = s3_key.strip('/')
        # Open the local file as a file-like object
        with open(file_path, 'rb') as file:
            # Upload the file-like object to S3
            content_type = FILE_TYPE_BACKEND.get(format_type, LongMIMETypes.BIN_OCTET_STREAM)
            s3.put_object(Body=file, Bucket=s3_bucket, ContentType=content_type, Key=s3_key)

        # need to encode the file path as the link generated in S3 is encoded.
        file_url = 'https://{}/{}/{}'.format(
            settings.REGION_HOST,
            s3_bucket,
            urllib.parse.quote_plus(s3_key, safe='/')
        )
    except Exception as e:
        logger.error("{} {}".format(ErrorLogTypes.S3_FILE_UPLOAD, get_traceback(e)))
        error = 'Error uploading the file ({}) [{}]'.format(file_name, e)
    return file_url, error


def delete_from_s3(file_url, s3_bucket=settings.SAAS_S3_BUCKET):
    error = None
    try:
        s3 = boto3.resource(
            's3',
            region_name=settings.REGION_NAME,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
        )
        s3_path = file_url.split(s3_bucket, 1)[-1].strip('/')
        s3_path = urllib.parse.unquote_plus(s3_path)
        s3.Object(s3_bucket, s3_path).delete()
    except Exception as e:
        logger.error("{} Error deleting the file at {} {}".format(ErrorLogTypes.S3_FILE_DELETE, s3_path, get_traceback(e)))
        error = 'Error deleting the file at {} [{}]'.format(s3_path, e)
    return error


def download_from_s3(file_name, file_url, destination_dir=None, s3_bucket=settings.SAAS_S3_BUCKET):
    error = None
    try:
        if s3_bucket in file_url:
            s3_path = file_url.split(s3_bucket, 1)[-1].strip('/')
        else:
            parsed_url = urlparse(file_url or '', allow_fragments=False)
            if parsed_url.scheme == 'https':
                s3_bucket, s3_path = parsed_url.path.strip('/').split('/', 1)
            elif parsed_url.scheme == 's3':
                s3_bucket = parsed_url.netloc
                s3_path = parsed_url.path

        s3_path = urllib.parse.unquote_plus(s3_path)
        if settings.ENVIRONMENT_ENV in ['lambda']:
            s3 = boto3.client(
                's3',
                region_name=settings.REGION_NAME,
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
            )
            obj = s3.get_object(Bucket=s3_bucket, Key=s3_path)
            destination_path = io.BytesIO(obj['Body'].read())
        else:
            s3 = boto3.resource(
                's3',
                region_name=settings.REGION_NAME,
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
            )

            if destination_dir and not os.path.exists(destination_dir):
                os.makedirs(destination_dir)
            destination_path = os.path.join(destination_dir or settings.MEDIA_ROOT, file_name)

            s3.meta.client.download_file(
                s3_bucket,
                s3_path,
                destination_path
            )
    except Exception as e:
        destination_path = None
        error = f'Error downloading file {file_name}'
        logger.error("{} Error downloading the file from {} {}".format(ErrorLogTypes.S3_FILE_DOWNLOAD, s3_path, get_traceback(e)))
    return destination_path, error
