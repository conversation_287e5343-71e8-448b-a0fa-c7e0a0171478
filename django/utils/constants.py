from zoneinfo import ZoneInfo
from enum import Enum

IST_TZ = ZoneInfo('Asia/Kolkata')
UTC_TZ = ZoneInfo('UTC')


class ImageTypes(Enum):
    JPG = 'jpg'
    JPEG = 'jpeg'
    PNG = 'png'


class DocumentTypes(Enum):
    PDF = 'pdf'
    XLSX = 'xlsx'
    XLS = 'xls'
    DOCX = 'docx'
    DOC = 'doc'


class VideoTypes(Enum):
    MP4 = 'mp4'
    MOV = 'mov'
    WEBM = 'webm'


# Dynamically create FileTypes Enum
def create_combined_enum(name, *enums):
    combined = {item.name: item.value for enum in enums for item in enum}
    return Enum(name, combined)


FileTypes = create_combined_enum("FileTypes", ImageTypes, DocumentTypes, VideoTypes)


class LongMIMETypes:
    MS_OFFICE = 'application/vnd.ms-office'
    MS_EXCEL = 'application/vnd.ms-excel'
    SHEET = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    MS_WORD = 'application/msword'
    DOCUMENT = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    OCTET_STREAM = 'application/octet-stream'
    BIN_OCTET_STREAM = 'binary/octet-stream'
    PDF = 'application/pdf'
    JPEG = 'image/jpeg'
    PNG = 'image/png'
    MP4 = 'video/mp4'
    MOV = 'video/mov'
    WEBM = 'video/webm'


class ShortMIMETypes:
    JPG = 'jpg'
    JPEG = 'jpeg'
    EXCEL = 'excel'
    XLSX = 'xlsx'
    XLS = 'xls'
    MS_WORD = 'msword'
    DOC = 'doc'
    DOCX = 'docx'
    ZIP = 'zip'
    DATA = 'data'
    ARCHIVE = 'archive'


FILE_TYPE_BACKEND = {
    FileTypes.JPG.value: LongMIMETypes.JPEG,
    FileTypes.JPEG.value: LongMIMETypes.JPEG,
    FileTypes.PNG.value: LongMIMETypes.PNG,
    FileTypes.PDF.value: LongMIMETypes.PDF,
    FileTypes.XLSX.value: LongMIMETypes.SHEET,
    FileTypes.XLS.value: LongMIMETypes.MS_EXCEL,
    FileTypes.DOCX.value: LongMIMETypes.MS_WORD,
    FileTypes.DOC.value: LongMIMETypes.MS_WORD,
    FileTypes.MP4.value: LongMIMETypes.MP4,
    FileTypes.MOV.value: LongMIMETypes.MOV,
    FileTypes.WEBM.value: LongMIMETypes.WEBM,
}

FILE_TYPE_HELPERS = {
    FileTypes.JPG.value: [ShortMIMETypes.JPG, ShortMIMETypes.JPEG, LongMIMETypes.JPEG],
    FileTypes.JPEG.value: [ShortMIMETypes.JPG, ShortMIMETypes.JPEG, LongMIMETypes.JPEG],
    FileTypes.PNG.value: [LongMIMETypes.PNG],
    FileTypes.PDF.value: [LongMIMETypes.PDF],
    FileTypes.XLSX.value: [
        ShortMIMETypes.EXCEL, ShortMIMETypes.XLSX, LongMIMETypes.MS_OFFICE,
        LongMIMETypes.SHEET, LongMIMETypes.MS_EXCEL, LongMIMETypes.OCTET_STREAM,
    ],
    FileTypes.XLS.value: [
        ShortMIMETypes.EXCEL, ShortMIMETypes.XLS, LongMIMETypes.MS_OFFICE,
        LongMIMETypes.MS_EXCEL, LongMIMETypes.OCTET_STREAM,
    ],
    FileTypes.DOCX.value: [
        ShortMIMETypes.DOC, ShortMIMETypes.DOCX, ShortMIMETypes.ZIP, ShortMIMETypes.DATA,
        ShortMIMETypes.ARCHIVE, LongMIMETypes.MS_WORD, LongMIMETypes.MS_OFFICE, LongMIMETypes.DOCUMENT
    ],
    FileTypes.DOC.value: [ShortMIMETypes.MS_WORD, ShortMIMETypes.DOC, LongMIMETypes.MS_WORD, LongMIMETypes.MS_OFFICE],
    FileTypes.MP4.value: [LongMIMETypes.MP4],
    FileTypes.MOV.value: [LongMIMETypes.MOV],
    FileTypes.WEBM.value: [LongMIMETypes.WEBM],
}


class SuccessMessages:
    SUCCESS = 'Success'
    OKAY = 'Looks like everything went okay'
    UPDATE_SUCCESS = 'Data updated successfully'
    RETRIEVE_SUCCESS = 'Data retrieved successfully'


class ErrorMessages:
    INVALID_TOKEN = 'Invalid token'
    ACCESS_DENIED = 'Access Denied'
    TECHNICAL_ERROR = 'OOPS!! Something went wrong. Please try again after sometime.'
    SMS_ALERT = '[SMS_ALERT_ERROR]'
    EMAIL_ALERT = '[EMAIL_ALERT_ERROR]'
    INVALID_FILTER_MODE = 'Missing or invalid filter mode'


class ErrorLogTypes:
    S3_FILE_UPLOAD = '[S3_FILE_UPLOAD_ERROR]'
    S3_FILE_DOWNLOAD = '[S3_FILE_DOWNLOAD_ERROR]'
    S3_FILE_DELETE = '[S3_FILE_DELETE_ERROR]'


class EmailType:
    OLD_VENDOR_OB_FORM = 'old_vendor_ob_form'
    NEW_VENDOR_OB_FORM = 'new_vendor_ob_form'
    VENDOR_OB_FORM_CORRECTIONS = 'vendor_ob_form_corrections'


class Status:
    FORBIDDEN = 403
    UNAUTHORIZED = 401
    BAD_REQUEST = 400
    SERVER_ERROR = 500
    NOT_FOUND = 404
    OK = 200


class SAASProduct(Enum):
    NETWORK = 1
    LOGISTICS_PROCUREMENT = 2
    OPTIMIZATION = 3
    EXECUTION = 4
    VISIBILITY = 5
    RECONCILIATION = 6
    ANALYTICS = 7
    ORCHESTRATION = 8
    ILMS = 9


class SAASModules(Enum):
    SUPER_ADMIN = 1
    ADMIN = 2
    PROCUREMENT = 3
    VENDOR_ONBOARDING = 4
    VENDOR_EXPLORER = 5
    FREIGHT_INDEX = 6
    CARBON_EMISSIONS = 7


class AppDomainID:
    AUTH = 'auth'
    ADMIN = 'admin'
    SUPERADMIN = 'superadmin'
    PROCUREMENT = 'procurement'
    NETWORK = 'network'


class AppPathID:
    # auth paths
    AUTH_LOGIN = 'auth_login'
    AUTH_VERIFY_EMAIL = 'auth_verify_email'
    AUTH_VERIFY_EMAIL_SUCCESS = 'auth_verify_email_success'
    AUTH_VERIFY_EMAIL_FAILED = 'auth_verify_email_failed'
    # superadmin paths
    SADMIN_CONFIG = 'sadmin_config'
    # admin paths
    ADMIN_CONFIRM_PLAN = 'admin_confirm_plan'
    ADMIN_PROFILE = 'admin_profile'
    # procurement paths
    PROCUREMENT_RFQ_DASHBOARD = 'procurement_rfq_dashboard'
    # network paths
    VENDOR_ONBOARDING_FORM = 'vendor_onboarding_form'


class AdminDBColls:
    COMPANY_TYPE = 'company_type'
    USER_TYPE = 'user_type'
    USER_ROLES = 'user_roles'
    PERMISSIONS = 'permissions'
    USER_PERMISSIONS = 'user_permissions'
    MODULES = 'modules'
    UI_CONFIG = 'ui_config'
    COMPANIES = 'companies'
    BRANCHES = 'branches'
    USERS = 'users'
    USER_SESSIONS = 'user_sessions'
    ACCESS_TOKENS = 'access_tokens'
    LOGIN_LOGS = 'login_logs'
    APP_URLS = 'app_urls'
    COMPANY_MAPPING = 'company_mapping'
    USER_ACTIVITY_LOGS = 'user_activity_logs'
    ACCESS_TOKEN = 'access_token'
    OTP_DETAILS = 'otp_details'
    BLOCKED_USERS = 'blocked_users'
    SMS_VENDORS = 'sms_vendors'
    SMS_SENT_LOGS = 'sms_sent_logs'
    EMAIL_LOGS = 'email_logs'
    BOUNCED_EMAILS = 'bounced_emails'
    API_REQUEST_LOGS = 'api_request_logs'
    STATES_LIST = 'states_list'
    TURNOVER_BRACKETS = 'turnover_brackets'
    VEHICLE_TYPES = 'vehicle_types'
    VEHICLE_BODY_TYPES = 'vehicle_body_types'
    PROVIDER_MAPPING = 'provider_mapping'
    SERVICES_OFFERED = 'services_offered'
    API_KEYS = 'api_keys'
    API_CONFIGS = 'api_configs'
    MODULE_CONFIGS = 'module_configs'
    EMAIL_CONFIGS = 'email_configs'
    ONBOARDING_DOCUMENT_TYPES = 'onboarding_document_types'


class NetworkDBColls:
    CELERY_SCHEDULES = 'celery_schedules'
    ONBOARDING_FORMS = 'onboarding_forms'
    ONBOARDING_FORM_REMARKS = 'onboarding_form_remarks'
    OB_FORM_ERRORS = 'ob_form_errors'


class CompanyType(Enum):
    SEEKER = 1
    PROVIDER = 2


class UserType(Enum):
    SEEKER = 1
    PROVIDER = 2
    DRIVER = 3
    CONSIGNEE = 4
    SCLEN = 5


class UserRole(Enum):
    SUPER_ADMIN = 1
    ADMIN_SEEKER = 10
    SEEKER = 20
    PROVIDER = 30
    ADMIN_PROVIDER = 40


class ServiceCategory(Enum):
    TRANSPORTER = 1
    FLEET_OWNER = 2


class SMSVendor:
    BOSCHINDIA = 2


class FilterModes(Enum):
    GET = 1
    APPLY = 2


class ApiType(Enum):
    GOOGLE_GEOCODE_API = 1
    GOOGLE_PLACES_API = 2


class BranchType(Enum):
    REGIONAL_OFFICE = 1
    BRANCH_OFFICE = 2


class Currency(Enum):
    INR = 'INR'


class AttachmentType(Enum):
    OFFICE = 'office_image_or_video'
    FLEET = 'fleet_image_or_video'


class OBFormSections(Enum):
    COMPANY_DETAILS = 'company_details'
    CONTACT_DETAILS = 'contact_details'
    ADMIN_DETAILS = 'admin_details'
    HEAD_OFFICE_DETAILS = 'head_office_details'
    REGIONAL_OFFICE_DETAILS = 'regional_office_details'
    BRANCH_OFFICE_DETAILS = 'branch_office_details'
    CUSTOMER_DETAILS = 'customer_details'
    MSME_DETAILS = 'msme_details'
    BANK_ACCOUNT_DETAILS = 'bank_account_details'
    SERVICE_CATEGORY = 'service_category'
    OPERATIONAL_AREAS = 'operational_areas'
    BUSINESS_SEGMENTS = 'business_segments'
    SERVICES_OFFERED = 'services_offered'
    VEHICLE_DETAILS = 'vehicle_details'
    OFFICE_IMAGE_OR_VIDEO = 'office_image_or_video'
    FLEET_IMAGE_OR_VIDEO = 'fleet_image_or_video'
    CANCELLED_CHEQUE = 'cancelled_cheque'
    TRADE_LICENSE = 'trade_license'
    CARRIER_CETIFICATE = 'carrier_cetificate'
    FSSAI_CERTIFICATE = 'fssai_certificate'
    MSME_CERTIFICATE = 'msme_certificate'
    PAN_CARD = 'pan_card'


class OBFormStatus(Enum):
    PENDING = 1
    DRAFT = 2
    REVIEW = 3
    APPROVED = 4
