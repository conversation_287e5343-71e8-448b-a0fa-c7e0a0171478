from utils.mongo import MongoUtility
from utils.constants import AdminDBColls, SAASModules
from .custom_exceptions import ConfigError


class Memoization:
    sms_vendor_configs = {}
    app_domains = {}
    app_paths = {}
    dropdowns_data = {}
    api_keys = {}
    api_configs = {}
    module_configs = {}

    @staticmethod
    def get_sms_vendor_config(vendor_type):
        if vendor_type in Memoization.sms_vendor_configs:
            return Memoization.sms_vendor_configs[vendor_type]

        db = MongoUtility(module_id=SAASModules.ADMIN.value)
        config = db.find(AdminDBColls.SMS_VENDORS, {'id': vendor_type}, find_one=True)
        Memoization.sms_vendor_configs[vendor_type] = config
        return config

    @staticmethod
    def get_dropdown_data(dropdown_coll, sort=[('order', 1), ('name', 1)]):
        if not Memoization.dropdowns_data.get(dropdown_coll):
            db = MongoUtility(module_id=SAASModules.ADMIN.value)
            df = {'_id': 0, 'is_active': 0, 'order': 0}
            Memoization.dropdowns_data[dropdown_coll] = [x for x in db.find(dropdown_coll, {'is_active': True}, df, sort=sort)]
        return Memoization.dropdowns_data[dropdown_coll]

    @staticmethod
    def get_api_key(module: SAASModules = SAASModules.VENDOR_ONBOARDING, service_key: str = 'google_api_key_be') -> str:
        if service_key not in Memoization.api_keys:
            db = MongoUtility(module_id=SAASModules.ADMIN.value)
            doc = db.find(AdminDBColls.API_KEYS, {'module_id': module.value}, find_one=True)
            try:
                Memoization.api_keys[service_key] = doc[service_key]
            except KeyError:
                raise ConfigError(f'Api Key not configured for {module.name} - {service_key}')
        return Memoization.api_keys[service_key]

    @staticmethod
    def get_api_config(api_type, module=SAASModules.PROCUREMENT):
        if api_type not in Memoization.api_configs:
            db = MongoUtility(module_id=SAASModules.ADMIN.value)
            query = {
                'module_id': module.value,
                'api_type': api_type.value,
                'is_active': True,
            }
            config = db.find(AdminDBColls.API_CONFIGS, query, find_one=True)
            if not config:
                raise ConfigError(f'Api details not configured for {api_type.value} - {api_type.name}')
            Memoization.api_configs[api_type] = config
        return Memoization.api_configs[api_type]

    @staticmethod
    def get_module_config(module=SAASModules.PROCUREMENT):
        module_id = module.value
        if module_id not in Memoization.module_configs:
            db = MongoUtility(module_id=SAASModules.ADMIN.value)
            query = {
                'module_id': module_id,
            }
            config = db.find(AdminDBColls.MODULE_CONFIGS, query, find_one=True)
            if not config:
                raise ConfigError(f'Module config unavailable for {module_id} - {module.name}')
            Memoization.module_configs[module_id] = config
        return Memoization.module_configs[module_id]

    @staticmethod
    def get_app_url_data(domain_id, path_id, **kwargs):
        if (domain_id not in Memoization.app_domains) or (path_id not in Memoization.app_paths):
            db = MongoUtility(module_id=SAASModules.ADMIN.value)
            obj = db.find(AdminDBColls.APP_URLS, {'id': domain_id}, find_one=True)

            if domain_id not in Memoization.app_domains:
                Memoization.app_domains[domain_id] = obj['fe_domain'].strip('/')

            for key, value in obj['fe_paths'].items():
                if key not in Memoization.app_paths:
                    Memoization.app_paths[key] = value

        domain = Memoization.app_domains[domain_id]
        path = Memoization.app_paths[path_id].format(**kwargs)

        return {
            'domain': domain,
            'path': path,
            'url': f'{domain}{path}',
        }
